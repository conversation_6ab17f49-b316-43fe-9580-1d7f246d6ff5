/* === DESKTOP FIRST === */

/* xl <= Extra extra large (xxl) */
@media (max-width : 1399.98px) {
	.topline-left .logo-wrapper {
		margin-right: 64px;
	}

	.topmenu-block {
		width: 560px;
	}

	.topmenu {
		width: 560px;
	}

	.section-registration {
		padding-top: 90px;
		padding-bottom: 156px;
	}

	.lottery-list-item-wrapper {
		width: 33.33%;
	}


	.topline--autorized .topmenu-block {
		width: 500px;
	}

	.topline--autorized .topmenu {
		width: 500px;
	}

	.lottery-list-items-empty {
		height: 570px;
	}

	.lottery-detail {
		flex-wrap: wrap;
	}

	.lottery-detail-left {
		width: 100%;
		padding-right: 0;
	}

	.lottery-detail-right {
		width: 100%;
		padding-top: 12px;
		display: flex;
		flex-direction: row-reverse;
	}

	.lottery-detail-right-bottom {
		width: 50%;
		padding-right: 6px;
	}

	.lottery-detail-right-top {
		width: 50%;
		padding-left: 6px;
	}

	.lottery-detail-ticket-table-block {
		margin-top: 0;
	}

	.related-lottery-slider {
		margin-left: -10px;
		margin-right: -10px;
	}

	.related-lottery-slide {
		padding-left: 10px;
		padding-right: 10px;
	}

	.related-lottery-slider .swiper-button-next {
		right: 10px;
	}

	.related-lottery-slider .swiper-button-prev {
		right: 62px;
	}

	.section-lottery-detail {
		padding-top: 40px;
	}

	.section-games-list {
		padding-top: 40px;
	}

	.db-page-topline__left .topmenu-block {
		width: 500px;
	}

	.db-page-topline__left .topmenu {
		width: 500px;
	}

	.dashboard {
		flex-wrap: wrap;
	}

	.dashboard-left {
		width: 100%;
	}

	.dashboard-right {
		width: 100%;
		padding-top: 12px;
		padding-left: 0;
	}

	.dashboard-info-stat-field-wrapper {
		width: 50%;
	}

	.dashboard-info-panel {
		display: flex;
		align-items: flex-end;
	}

	.dashboard-info-panel__top {
		width: calc(100% - 218px);
		padding-right: 12px;
	}

	.dashboard-info-panel__bottom {
		width: 218px;
	}


	.new-deposit {
		flex-wrap: wrap;
	}

	.new-deposit-left {
		width: 100%;
	}

	.new-deposit-right {
		width: 100%;
		padding-top: 12px;
		padding-left: 0;
	}

	.new-deposit-info-stat-item-wrapper {
		width: 33.33%;
	}

	.new-deposit-info-stat-items-block {
		max-width: none;
	}

	.db-affiliate {
		flex-wrap: wrap;
	}

	.db-affiliate-left {
		width: 100%;
	}

	.db-affiliate-right {
		width: 100%;
		padding-top: 12px;
		padding-left: 0;
	}


	.setting {
		flex-wrap: wrap;
	}

	.setting-left {
		width: 100%;
	}

	.setting-right {
		width: 100%;
		padding-top: 12px;
		padding-left: 0;
	}

	.front-top-slide-item-image-block {}

	.front-top-slide-item-image-diamond {
		left: auto;
		right: 87px;
	}

	.front-top-feature-wrapper {
		width: 50%;
	}

	.front-staking__image {
		bottom: -258px;
		right: -314px;
	}

	.front-staking {
		padding: 40px;
	}

	.section-about-us {
		padding-top: 40px;
	}

	.about-us-top-descr {
		max-width: 100%;
	}

	.profit-generate-scheme-profit-item-title {
		max-width: 120px;
	}

	.section-staking {
		padding-top: 40px;
	}

	.section-affiliate {
		padding-top: 40px;
	}

	.section-news {
		padding-top: 40px;
	}

	.section-contacts {
		padding-top: 40px;
	}

	.contacts-block {
		margin-top: 40px;
	}

	.section-faq {
		padding-top: 40px;
	}


	.section-news {
		padding-top: 40px;
	}

	.section-news-single {
		padding-top: 40px;
	}

	.promotion-item-wrapper {
		width: 33.33%;
	}

	.section-promotions {
		padding-top: 40px;
	}
}

/* lg <= Extra large (xl) */
@media (max-width : 1199.98px) {
	.topmenu-block {
		width: 400px;
	}

	.topmenu {
		width: 400px;
	}

	.topline-left .logo-wrapper {
		margin-right: 32px;
	}

	.registration-right {
		padding: 48px 24px;
	}

	.topline--autorized .topmenu-block {
		width: 316px;
	}

	.topline--autorized .topmenu {
		width: 316px;
	}

	.lottery-list-item-wrapper {
		padding-left: 4px;
		padding-right: 4px;
		margin-top: 8px;
	}

	.lottery-list-items {
		margin-left: -4px;
		margin-right: -4px;
		margin-top: -8px;
	}

	.lottery-list-filter-form-fund-col .field {
		width: 182px;
	}

	.section-lottery-list {
		padding-top: 40px;
	}

	.s-lottery-create-right {
		width: 376px;
	}

	.s-lottery-create-left {
		width: calc(100% - 376px);
	}

	.lottery-create-form-payment-block {
		max-width: 100%;
	}

	.section-lottery-create {
		padding-top: 40px;
	}

	.games-list-filter-form-col--category {
		width: 227px;
	}

	.games-list-filter-form-col--search {
		width: calc(100% - 509px);
	}

	.game-roulette-bet-buttons {
		margin-left: -1px;
		margin-right: -1px;
		margin-top: -2px;
	}

	.game-roulette-bet-left {
		max-width: 40px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-right {
		max-width: 40px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center-left {
		max-width: 162px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center {
		max-width: 162px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-btn-wrapper {
		padding-left: 1px;
		padding-right: 1px;
		margin-top: 2px;
	}

	.game-roulette-bet-btn {
		width: 38px;
		height: 38px;
		font-size: 12px;
	}

	.game-roulette-bet-center-right {
		max-width: 162px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-btn--zero {
		height: 118px;
	}

	.game-roulette-bet-btn--long {
		width: 160px;
	}

	.game-roulette-bet-btn--middle {
		width: 79px;
	}

	.game-panel-block {}

	.game-panel-block--roulette {
		padding-top: 36px;
		padding-bottom: 36px;
	}

	.db-lottery-t-h-col--time,
	.db-lottery-t-i-col--time {
		width: 18%;
	}

	.db-lottery-t-h-col--lottery,
	.db-lottery-t-i-col--lottery {
		width: 22%;
	}

	.db-deposits-t-i-col--total-profit {
		width: 25%;
	}

	.db-deposits-t-i-col--start-date {
		width: 25.25%;
	}

	.db-deposits-t-h-col--total-profit {
		width: 25%;
	}

	.db-deposits-t-h-col--start-date {
		width: 23.25%;
	}

	.db-page-topline__left .topmenu-block {
		width: 325px;
	}

	.db-page-topline__left .topmenu {
		width: 325px;
	}

	.new-deposit-step-list-item-sep {
		width: 40px;
	}


	.deposits-t-h-col--plan {
		width: 21%;
	}

	.deposits-t-h-col--total-profit {
		width: 21%;
	}

	.deposits-t-h-col--duration {
		width: 10.25%;
	}

	.deposits-t-h-col--start-date {

		width: 20.75%;
	}

	.deposits-t-i-col--plan {
		width: 21%;
	}

	.deposits-t-i-col--total-profit {
		width: 21%;
	}

	.deposits-t-i-col--duration {
		width: 12.25%;
	}

	.deposits-t-i-col--start-date {
		width: 20.75%;
	}

	.refill-left {
		display: none;
	}

	.refill-right {
		width: 100%;
		padding-left: 0;
	}

	.withdrawal-left {
		display: none;
	}

	.withdrawal-right {
		width: 100%;
		padding-left: 0;
	}

	.withdrawal-content-block {
		padding: 40px;
	}

	.db-affiliate-program-t-h-col--structure {
		width: calc(21% - 10px);
	}

	.db-affiliate-program-t-h-col--level {
		width: calc(11.28% - 10px);
	}

	.db-affiliate-program-t-i-col--structure {
		width: calc(21% - 10px);
	}

	.db-affiliate-program-t-i-col--level {
		width: calc(11.28% - 10px);
	}


	.db-affiliate-program-t-item.active:nth-child(1)::before,
	.db-affiliate-program-t-item.active:nth-child(2)::before {
		width: calc(32.28% + 60px);
	}

	.db-affiliate-program-t-item.active:nth-child(3)::before,
	.db-affiliate-program-t-item.active:nth-child(4)::before,
	.db-affiliate-program-t-item.active:nth-child(5)::before {
		width: calc(54.84% + 40px);
	}

	.db-affiliate-program-t-item.active:nth-child(6)::before,
	.db-affiliate-program-t-item.active:nth-child(7)::before {
		width: calc(77.4% + 20px);
	}


	.db-wallets-t-h-col--payment,
	.db-wallets-t-i-col--payment {
		width: calc(24% - 22px);
	}

	.db-wallets-t-h-col--date,
	.db-wallets-t-i-col--date {
		width: calc(26% - 22px);
	}

	.footer-menu-block {
		width: 18%;
	}

	.footer-menu-block--terms {
		width: 24%;
	}

	.footer-menu-block--contact {
		width: 40%;
	}

	.start-earning-image-right {
		right: -270px;
	}

	.start-earning-image-left {
		left: -89px;
	}

	.about-us-vision {
		padding-bottom: 112px;
	}

	.about-us-vision__btn-block {
		bottom: 32px;
	}

	.about-offer-item-wrapper {
		width: 50%;
	}

	.about-community {
		padding: 40px;
	}

	.about-community-right {
		width: 50%;
		padding-left: 12px;
	}

	.about-community-left {
		width: 50%;
		padding-right: 12px;
	}

	.news-item-wrapper {
		width: 50%;
	}

	.news-items-block {
		margin-top: 40px;
	}

	.contacts-left {
		width: 455px;
	}

	.contacts-right {
		width: calc(100% - 455px);
	}

	.contacts-form-block {
		padding: 48px;
	}

	.faq-category-items {
		margin-left: -12px;
		margin-right: -12px;
		margin-top: -24px;
	}

	.faq-category-item-wrapper {
		margin-top: 24px;
		padding-left: 12px;
		padding-right: 12px;
	}

	.db-empty-elements-block {
		min-height: 400px;
	}

	.cookie-block {
		max-width: 940px;
	}

	.game-bet-slider-block {
		width: 366px;
	}

	.game-seed-item__left {
		width: 474px;
	}

	.game-seed-item__right {
		width: calc(100% - 474px);
		padding-left: 16px;
	}

	.front-top-slide-item-image {
		left: auto;
		transform: translateY(-50%);
		right: 24px;
	}

	.front-top-slide-item-content {
		max-width: 420px;
	}

	.front-top-slide-item-image {
		width: 346px;
		height: 296px;
	}
}

/* md <= Large (lg) */
@media (max-width : 991.98px) {

	.topmenu-block {
		display: none;
	}

	.mobile-panel-btn-block {
		display: block;
	}

	.section-registration {
		padding-top: 64px;
		padding-bottom: 90px;
	}

	.mobile-panel-block {
		display: block;
	}

	.lottery-list-item-wrapper {
		margin-top: 24px;
		padding-left: 12px;
		padding-right: 12px;
		width: 50%;
	}

	.lottery-list-items {
		margin-left: -12px;
		margin-right: -12px;
		margin-top: -24px;
	}

	.lottery-list-top {
		flex-wrap: wrap;
	}

	.lottery-list-top-left {
		width: 100%;
		padding-right: 0;
	}

	.lottery-list-top-right {
		padding-top: 24px;
	}

	.topline .topline-balance-block {
		display: none;
	}

	.topline-balance-dropdown {
		margin-top: 6px !important;
	}

	.lottery-detail-info {
		padding: 24px;
	}

	.lottery-detail-info-progress-block {
		margin-top: 16px;
	}

	.lottery-detail-info-items-block {
		margin-top: 16px;
	}

	.lottery-detail-info-items {
		margin-top: -8px;
		margin-left: -4px;
		margin-right: -4px;
	}

	.lottery-detail-info-item-wrapper {
		flex-grow: auto;
		width: 25%;
		padding-left: 4px;
		padding-right: 4px;
		margin-top: 8px;
	}

	.lottery-detail-info-item {
		padding: 24px 12px;
	}

	.lottery-detail-info-purchase-form-block {
		margin-top: 16px;
	}

	.lottery-detail-participants-block {
		margin-top: 16px;
	}

	.lottery-detail-download {
		padding: 12px;
	}

	.lottery-detail-guarantee-btn {
		padding: 16px 12px;
		align-items: center;
	}

	.lottery-detail-info-countdown-block {
		margin-top: 16px;
	}

	.lottery-detail-info-status-block {
		margin-top: 16px;
	}

	.s-lottery-create {
		flex-wrap: wrap;
	}

	.s-lottery-create-left {
		width: 100%;
		padding-right: 0;
	}

	.s-lottery-create-right {
		width: 100%;
		padding-top: 24px;
	}

	.lottery-create-banner {
		padding-top: 82px;
		padding-bottom: 82px;
	}

	.lottery-create-banner-bg-image {
		width: 330px;
		height: 300px;
		top: 32px;
		right: 24px;
		left: auto;
		bottom: auto;
	}

	.lottery-create-banner-info-slider-block {
		margin-left: 0;
	}

	.lottery-create-banner-info-slide-title {
		text-align: left;
	}

	.lottery-create-banner-info-slider .swiper-pagination {
		justify-content: flex-start;
	}

	.games-bet-t-h-col {
		padding: 16px 8px;
	}

	.games-bet-t-i-col {
		padding: 15px 7px;
	}

	.games-bet-t-h-col--time,
	.games-bet-t-i-col--time {
		width: 14.66%;
	}

	.games-bet-t-h-col--bet,
	.games-bet-t-i-col--bet {
		width: 18.66%;
	}

	.games-bet-t-h-col--multiplier,
	.games-bet-t-i-col--multiplier {
		width: 14.66%;
	}

	.games-bet-t-h-col--payout,
	.games-bet-t-i-col--payout {
		width: 18.66%;
	}



	.games-list-filter-form-col--category {
		width: 136px;
	}

	.games-list-filter-form-col--search {
		width: calc(100% - 418px);
	}


	.games-wins-t-h-col {
		padding: 16px 8px;
	}

	.games-wins-t-i-col {
		padding: 15px 7px;
	}

	.games-wins-t-h-col--date,
	.games-wins-t-i-col--date {
		width: 14.66%;
	}

	.games-wins-t-h-col--bet,
	.games-wins-t-i-col--bet {
		width: 18.66%;
	}

	.games-wins-t-h-col--multiplier,
	.games-wins-t-i-col--multiplier {
		width: 14.66%;
	}

	.games-wins-t-h-col--payout,
	.games-wins-t-i-col--payout {
		width: 18.66%;
	}

	.game-section {
		flex-direction: column-reverse;
	}

	.game-panel-block {
		padding-top: 12px;
		padding-bottom: 12px;
	}

	.game-section__right {
		width: 100%;
		padding-left: 0;
	}

	.game-section__left {
		width: 100%;
		padding-top: 12px;
	}

	.game-description__left {
		width: 144px;
	}

	.game-description__right {
		width: calc(100% - 144px);
	}



	.db-games-t-h-col {
		padding: 16px 8px;
	}

	.db-games-t-i-col {
		padding: 15px 7px;
	}

	.db-games-t-h-col--time,
	.db-games-t-i-col--time {
		width: 14.66%;
	}

	.db-games-t-h-col--bet,
	.db-games-t-i-col--bet {
		width: 18.66%;
	}

	.db-games-t-h-col--multiplier,
	.db-games-t-i-col--multiplier {
		width: 14.66%;
	}

	.db-games-t-h-col--payout,
	.db-games-t-i-col--payout {
		width: 18.66%;
	}


	.db-page--wide-menu {}

	.db-page--wide-menu .db-page-left {
		width: 86px;
	}

	.db-page--wide-menu .db-page-right {
		width: calc(100% - 86px);
	}

	.db-page--wide-menu .db-side-block {
		padding-left: 4px;
		padding-right: 4px;
	}


	.db-page--wide-menu .db-side-logo__text-block {
		width: 0;
	}

	.db-page--wide-menu .db-side-toggle-panel-btn__text-block {
		width: 0;
	}

	.db-page--wide-menu .db-sidemenu-link__text-block {
		width: 0;
	}

	.db-lottery-t-heading {
		display: none;
	}


	.db-lottery-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-lottery-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-lottery-t-item:first-child {
		margin-top: 0;
	}

	.db-lottery-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-lottery-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-lottery-t-i-col__title-block {
		display: block;
	}

	.db-lottery-t-i-col__value-block {
		width: auto;
	}



	.db-games-t-heading {
		display: none;
	}


	.db-games-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-games-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-games-t-item:first-child {
		margin-top: 0;
	}

	.db-games-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-games-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-games-t-i-col__title-block {
		display: block;
	}

	.db-games-t-i-col__value-block {
		width: auto;
	}


	.db-deposits-t-heading {
		display: none;
	}


	.db-deposits-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-deposits-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-deposits-t-item:first-child {
		margin-top: 0;
	}

	.db-deposits-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-deposits-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-deposits-t-i-col__title-block {
		display: block;
	}

	.db-deposits-t-i-col__value-block {
		width: auto;
	}

	.db-deposits-t-i-details-btn {
		width: 24px;
		height: 24px;
		padding: 0;
	}

	.db-deposits-t-i-details-btn::before {
		width: 20px;
		height: 20px;
		mask-size: 20px 20px;
		-webkit-mask-size: 20px 20px;
	}

	.db-deposits-t-i-plan__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-deposits-t-i-plan {
		flex-direction: row-reverse;
	}

	.db-deposits-t-i-profit {
		flex-direction: row-reverse;
	}

	.db-deposits-t-i-profit__content {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-page-topline__left {
		display: none;
	}

	.db-page-topline__right {
		width: 100%;
	}

	.db-page-topline-panel {
		justify-content: space-between;
	}

	.db-page-topline-panel__left {
		flex-direction: row-reverse;
	}

	.topline-refill-btn-block {
		padding-left: 0;
		padding-right: 8px;
	}

	.dashboard-info-panel {
		flex-wrap: wrap;
	}

	.dashboard-info-panel__top {
		width: 100%;
		padding-right: 0;
	}

	.dashboard-info-panel__bottom {
		width: 280px;
	}

	.new-deposit-step-list-item-sep {
		display: none;
	}

	.new-dep-ps-radio-item-wrapper {
		width: 50%;
	}

	.new-dep-inv-plan-radio-item-wrapper {
		width: 50%;
	}


	.deposits-t-heading {
		display: none;
	}


	.deposits-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.deposits-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.deposits-t-item:first-child {
		margin-top: 0;
	}

	.deposits-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.deposits-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.deposits-t-i-col__title-block {
		display: block;
	}

	.deposits-t-i-col__value-block {
		width: auto;
	}

	.deposits-t-i-details-btn {
		width: 24px;
		height: 24px;
		padding: 0;
	}

	.deposits-t-i-details-btn::before {
		width: 20px;
		height: 20px;
		mask-size: 20px 20px;
		-webkit-mask-size: 20px 20px;
	}

	.deposits-t-i-plan__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.deposits-t-i-plan {
		flex-direction: row-reverse;
	}

	.deposits-t-i-profit {
		flex-direction: row-reverse;
	}

	.deposits-t-i-profit__content {
		padding-left: 0;
		padding-right: 8px;
	}

	.deposits-filter-form-col {
		width: 33.33%;
	}

	.withdrawal-content-block {
		padding: 24px;
	}

	.transactions-filter-form-col {
		width: 33.33%;
	}

	.transactions-table {
		min-width: 0;
	}

	.transactions-t-heading {
		display: none;
	}


	.transactions-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.transactions-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.transactions-t-item:first-child {
		margin-top: 0;
	}

	.transactions-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.transactions-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.transactions-t-i-col__title-block {
		display: block;
	}

	.transactions-t-i-col__value-block {
		width: auto;
	}

	.transactions-t-i-col--description .transactions-t-i-col__value-block {
		max-width: 50%;
	}

	.transactions-t-i-description {
		text-align: right;
	}

	.db-side-toggle-panel-btn-block {
		display: none;
	}

	.db-side-tablet-panel-btn-block {
		display: block;
	}

	.db-side-tablet-block {
		display: block;
	}

	.db-affiliate-tabs {
		margin-left: 0;
		margin-right: 0;
	}

	.db-affiliate-tab-wrapper {
		padding-left: 0;
		padding-right: 0;
	}

	.db-affiliate-tab {
		padding-left: 6px;
		padding-right: 6px;
	}

	.db-affiliate-tab__title {
		font-size: 16px;
	}


	.db-affiliate-partners-t-heading {
		display: none;
	}


	.db-affiliate-partners-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-affiliate-partners-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-affiliate-partners-t-item:first-child {
		margin-top: 0;
	}

	.db-affiliate-partners-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-affiliate-partners-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-affiliate-partners-t-i-col__title-block {
		display: block;
	}

	.db-affiliate-partners-t-i-col__value-block {
		width: auto;
	}

	.db-affiliate-partners-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-affiliate-partners-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.modal--partner .modal-dialog {
		max-width: 690px;
	}


	.setting-tabs {
		margin-left: 0;
		margin-right: 0;
	}

	.setting-tab-wrapper {
		padding-left: 0;
		padding-right: 0;
	}

	.setting-tab {
		padding-left: 6px;
		padding-right: 6px;
	}

	.setting-tab__title {
		font-size: 16px;
	}




	.db-wallets-t-heading {
		display: none;
	}


	.db-wallets-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-wallets-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-wallets-t-item:first-child {
		margin-top: 0;
	}

	.db-wallets-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-wallets-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-wallets-t-i-col__title-block {
		display: block;
	}

	.db-wallets-t-i-col__value-block {
		width: auto;
	}

	.db-wallets-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-wallets-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-autorisations-t-heading {
		display: none;
	}


	.db-autorisations-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-autorisations-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-autorisations-t-item:first-child {
		margin-top: 0;
	}

	.db-autorisations-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-autorisations-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-autorisations-t-i-col__title-block {
		display: block;
	}

	.db-autorisations-t-i-col__value-block {
		width: auto;
	}

	.db-autorisations-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-autorisations-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.front-top-slide-item-content {
		max-width: 385px;
	}

	.front-top-slide-item-image-block {
		width: 100%;
	}

	.front-top-slide-item-image{
		width: 200px;
		height: 170px;
		right: 80px;
		bottom: 20px;
		transform: none;
		top: auto;
	}

	.front-top-feature-wrapper {
		margin-top: 12px;
		padding-left: 6px;
		padding-right: 6px;
	}

	.front-top-features {
		margin-top: -12px;
		margin-left: -6px;
		margin-right: -6px;
	}

	.front-staking__image {
		width: 490px;
		right: -180px;
		bottom: -300px;
	}

	.join-telegram-block {
		margin-top: 24px;
		height: auto;
	}

	.join-telegram {
		height: auto;
		padding: 24px;
	}

	.start-earning-image-right {
		width: 312px;
		top: auto;
		bottom: -140px;
		right: -106px;
	}

	.footer-menu-block--product {
		display: none;
	}

	.footer-menu-block--support {
		display: none;
	}

	.footer-menu-block--terms {
		width: 43%;
	}

	.footer-menu-block--contact {
		width: 57%;
	}

	.about-community {
		flex-wrap: wrap;
	}

	.about-community-left {
		width: 100%;
		padding-right: 0;
	}

	.about-community-right {
		width: 100%;
		padding-left: 0;
		padding-top: 24px;
	}

	.about-choose-item-wrapper {
		width: 100%;
	}

	.staking-benefit-item-wrapper {
		width: 33.33%;
	}

	.staking-benefit-item__text {
		padding-right: 0;
	}

	.staking-benefit-item__text br {
		display: none;
	}

	.staking-offer-item-wrapper {
		width: 50%;
	}

	.front-features-title {
		text-align: center;
	}

	.front-feature-items {
		flex-wrap: wrap;
	}

	.front-feature-item-wrapper {
		width: 100%;
	}

	.front-work-items {
		flex-wrap: wrap;
	}

	.front-work-item-wrapper {
		width: 100%;
	}

	.calculator-form-col {
		width: 100%;
	}

	.calculator-form-col--button {
		padding-top: 16px;
	}

	.calculator-result-item-wrapper {
		width: 50%;
	}

	.profit-generate-title {
		text-align: center;
	}

	.profit-generate-scheme {
		flex-wrap: wrap;
		justify-content: center;
	}

	.profit-generate-scheme-step {
		width: 100%;
	}

	.profit-generate-scheme-development {
		display: flex;
		flex-direction: column;
	}

	.profit-generate-scheme-development-title {
		order: 0;
		margin-top: 0;
	}

	.profit-generate-scheme-development-descr {
		order: 1;
	}

	.profit-generate-scheme-development-image {
		margin-top: 16px;
		order: 2;
	}

	.profit-generate-scheme-transition {
		width: 48px;
		height: auto;
		flex-direction: column;
		align-items: center;
		padding-top: 8px;
		padding-bottom: 8px;
	}

	.profit-generate-scheme-transition-arrow {
		width: 8px;
		height: 32px;
	}

	.profit-generate-scheme-transition-arrow::before {
		width: 8px;
		height: 32px;
		mask-size: 8px 32px;
		-webkit-mask-size: 8px 32px;
		mask-image: url('../images/svg/arrow-long-down.svg');
		-webkit-mask-image: url('../images/svg/arrow-long-down.svg');
	}

	.profit-generate-scheme-transition-icon-block {
		padding-left: 0;
		width: 32px;
		padding-top: 8px;
		height: auto;
	}


	.contacts-left {
		width: 335px;
	}

	.contacts-right {
		width: calc(100% - 335px);
	}

	.contacts-info-block {
		padding: 48px 32px;
	}

	.contacts-form-block {
		padding: 48px 32px;
	}

	.section-faq+.section-start-earning .start-earning-image-right {
		right: -106px;
	}

	.section-news-single+.section-start-earning .start-earning-image-right {
		right: -106px;
	}

	.faq-category-top-descr {
		margin-top: 12px;
	}

	.faq-category-title-block {
		margin-top: 12px;
	}

	.promotions-filter-form-type-items {
		margin-left: 0;
		margin-right: 0;
	}

	.promotions-filter-form-type-item-wrapper {
		padding-left: 0;
		padding-right: 0;
	}

	.promotions-filter-form-col--left {
		width: auto;
		max-width: calc(100% - 130px);
	}

	.promotions-filter-form-col--right {
		width: 130px;
	}


	.db-affiliate-banners-t-heading {
		display: none;
	}


	.db-affiliate-banners-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-affiliate-banners-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-affiliate-banners-t-item:first-child {
		margin-top: 0;
	}

	.db-affiliate-banners-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-affiliate-banners-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-affiliate-banners-t-i-col__title-block {
		display: block;
	}

	.db-affiliate-banners-t-i-col__value-block {
		width: auto;
	}

	.db-affiliate-banners-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-affiliate-banners-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}


	.db-affiliate-promo-pdf-t-heading {
		display: none;
	}


	.db-affiliate-promo-pdf-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-affiliate-promo-pdf-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-affiliate-promo-pdf-t-item:first-child {
		margin-top: 0;
	}

	.db-affiliate-promo-pdf-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-affiliate-promo-pdf-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-affiliate-promo-pdf-t-i-col__title-block {
		display: block;
	}

	.db-affiliate-promo-pdf-t-i-col__value-block {
		width: auto;
	}

	.db-affiliate-promo-pdf-t-i-description {
		text-align: right;
	}

	.db-affiliate-promo-pdf-t-i-title {
		flex-direction: row-reverse;
	}

	.db-affiliate-promo-pdf-t-i-title__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-affiliate-promo-pdf-t-i-lang {
		flex-direction: row-reverse;
	}

	.db-affiliate-promo-pdf-t-i-lang__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-affiliate-banners-t-i-title {
		flex-direction: row-reverse;
	}

	.db-affiliate-banners-t-i-title__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-affiliate-banners-t-i-lang {
		flex-direction: row-reverse;
	}

	.db-affiliate-banners-t-i-lang__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.deposits-empty-item__descr br {
		display: none;
	}

	.cookie-block {
		max-width: 696px;
	}

	.available-currency-list {
		margin-left: 0;
		margin-right: 0;
	}

	.available-currency-list-item-wrapper {
		margin-top: 0;
		padding-left: 0;
		padding-right: 0;
	}

	.game-seed-item__left {
		width: 100%;
	}

	.game-seed-item__right {
		width: 100%;
		padding-left: 0;
		padding-top: 16px;
	}

	.db-address-whitelist-t-heading {
		display: none;
	}


	.db-address-whitelist-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-address-whitelist-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-address-whitelist-t-item:first-child {
		margin-top: 0;
	}

	.db-address-whitelist-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-address-whitelist-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-address-whitelist-t-i-col__title-block {
		display: block;
	}

	.db-address-whitelist-t-i-col__value-block {
		width: auto;
		max-width: 70%;
	}

	.db-address-whitelist-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-address-whitelist-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.game-panel--dice .game-panel__middle {
		padding-top: 55px;
		padding-bottom: 60px;
	}

	.game-manage-panel {
		flex-direction: column-reverse;
	}

	.game-manage-tab-content {
		padding-top: 0;
		padding-bottom: 12px;
	}

	.db-affiliate-program-t-i-col {
		padding: 15px 8px;
	}

	.db-affiliate-program-t-h-col {
		padding: 16px 8px;
	}

	.topline-left .logo-wrapper {
		margin-right: 12px;
	}

	.topline-lang-panel-block:first-child {
		padding-left: 0;
	}


	.front-top-slide-item-content {
		width: 100%;
		max-width: 100%;
	}
}

/* sm <= Medium (md) */
@media (max-width : 767.98px) {
	.section-registration {
		padding-top: 32px;
		padding-bottom: 70px;
	}

	.registration {
		flex-wrap: wrap;
	}

	.registration-left {
		width: 100%;
		padding: 20px;
		border-radius: 24px 24px 0 0;
	}

	.registration-upline-block {
		margin-right: 0;
		max-width: 285px;
	}

	.registration-feature-items-block {
		display: none;
	}

	.registration-left-bg-image {
		display: none;
	}

	.registration-right {
		width: 100%;
		padding: 24px;
	}

	.registration-mobile-feature-items-block {
		display: block;
	}

	.lottery-list-item-wrapper {
		padding-left: 4px;
		padding-right: 4px;
		margin-top: 8px;
	}

	.lottery-list-items {
		margin-left: -4px;
		margin-right: -4px;
		margin-top: -8px;
	}

	.lottery-list-item__ticket-count-item {
		padding: 7px 8px;
	}

	.lottery-list-item {
		padding: 24px 16px;
	}

	.lottery-list-filter-panel-btn-block {
		display: block;
	}

	.lottery-list-filter-form-type-item-wrapper {
		flex-grow: 1;
	}

	.lottery-list-filter-form-type-item {
		text-align: center;
		padding-top: 15px;
		padding-bottom: 15px;
	}

	.lottery-list-filter-form-row {
		margin-left: -4px;
		margin-right: -4px;
	}

	.lottery-list-filter-form-col {
		padding-left: 4px;
		padding-right: 4px;
	}

	.lottery-list-filter-form-col--type {
		width: 64%;
	}

	.lottery-list-filter-form-col--currency {
		width: 36%;
	}

	.lottery-list-filter-form-col--price {
		width: 100%;
	}

	.lottery-list-filter-form-price-col--title {
		width: 60px;
	}

	.lottery-list-filter-form-price-col--field {
		width: calc(50% - 30px);
	}

	.lottery-list-filter-form-price-col .field {
		width: 100%;
	}

	.lottery-list-filter-form-col--fund {
		width: 100%;
	}

	.lottery-list-filter-form-fund-col--title {
		width: 60px;
	}

	.lottery-list-filter-form-fund-col--field {
		width: calc(100% - 60px);
	}

	.lottery-list-filter-form-fund-col .field {
		width: 100%;
	}

	.lottery-list-filter-form-col--checkboxes {
		width: 100%;
	}

	.lottery-list-filter-form-col--clear {
		width: 100%;
	}

	.lottery-list-filter-panel {
		display: none;
	}

	.lottery-list-filter-panel.active {
		display: block;
	}

	.lottery-list-filter-form-col--currency .bootstrap-select>.dropdown-menu {
		inset: 0px 0px auto auto !important;
	}

	.lottery-detail-info-items {
		flex-wrap: wrap;
	}

	.lottery-detail-info-item-wrapper {
		width: 50%;
	}

	.lottery-detail-info-purchase-form__btn-block {
		width: 50%;
		padding-left: 8px;
	}

	.lottery-detail-info-purchase-form__field {
		width: 50%;
		padding-right: 8px;
	}

	.lottery-detail-right {
		flex-direction: column-reverse;
	}

	.lottery-detail-right-bottom {
		width: 100%;
		padding-right: 0;
	}

	.lottery-detail-right-top {
		width: 100%;
		padding-left: 0;
		padding-top: 12px;
	}

	.lottery-detail-guarantee-btn {
		padding: 24px 12px;
	}

	.lottery-create-block {
		padding: 16px;
		padding-bottom: 32px;
	}

	.lottery-create-banner {
		padding-left: 24px;
		padding-right: 24px;
	}

	.lottery-create-banner-bg-image {
		right: -64px;
	}


	.games-list-filter-form-col--type {
		width: 282px;
		order: 1;
	}

	.games-list-filter-form-col--category {
		width: calc(100% - 282px);
		order: 2;
	}

	.games-list-filter-form-col--search {
		width: 100%;
		order: 3;
	}

	.games-list-filter-form-row {
		flex-wrap: wrap;
		margin-top: -12px;
	}

	.games-list-filter-form-col {
		margin-top: 12px;
	}

	.games-bet-top__right {
		display: none;
	}

	.games-bet-tab {
		padding-left: 4px;
		padding-right: 4px;
	}

	.games-bet-t-heading {
		display: none;
	}

	.games-bet-t-item {
		display: block;
	}

	.games-bet-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.games-bet-t-i-col__title-block {
		display: block;
		padding-right: 4px;
	}

	.games-bet-t-i-col__value-block {
		width: auto;
	}

	.games-bet-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.games-bet-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.games-bet-t-item {
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.games-bet-t-item:first-child {
		margin-top: 0;
	}


	.games-leader-t-heading {
		display: none;
	}

	.games-leader-t-item {
		display: block;
	}

	.games-leader-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.games-leader-t-i-col__title-block {
		display: block;
		padding-right: 4px;
	}

	.games-leader-t-i-col__value-block {
		width: auto;
	}

	.games-leader-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.games-leader-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.games-leader-t-item {
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.games-leader-t-item:first-child {
		margin-top: 0;
	}


	.games-wins-t-heading {
		display: none;
	}

	.games-wins-t-item {
		display: block;
	}

	.games-wins-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.games-wins-t-i-col__title-block {
		display: block;
		padding-right: 4px;
	}

	.games-wins-t-i-col__value-block {
		width: auto;
	}

	.games-wins-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.games-wins-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.games-wins-t-item {
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.games-wins-t-item:first-child {
		margin-top: 0;
	}


	.game-roulette-bet-buttons {
		margin-left: -1px;
		margin-right: -1px;
		margin-top: -2px;
	}

	.game-roulette-bet-left {
		max-width: 34px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-right {
		max-width: 34px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center-left {
		max-width: 136px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center {
		max-width: 136px;
		padding-left: 1px;
		padding-right: 1px;
	}


	.game-roulette-bet-btn {
		width: 32px;
		height: 32px;
		font-size: 10px;
		border-radius: 6px;
	}

	.game-roulette-bet-center-right {
		max-width: 136px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-btn--zero {
		height: 100px;
	}

	.game-roulette-bet-btn--long {
		width: 134px;
	}

	.game-roulette-bet-btn--middle {
		width: 66px;
	}

	.game-description {
		flex-wrap: wrap;
	}

	.game-description__left {
		width: 100%;
		padding-right: 0;
	}

	.game-description__right {
		width: 100%;
		padding-right: 0;
		padding-top: 24px;
	}

	.game-description__image {
		max-width: 120px;
	}

	.modal--bet .modal-content {
		padding: 32px;
	}


	.db-games-t-heading {
		display: none;
	}

	.db-games-t-item {
		display: block;
	}

	.db-games-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-games-t-i-col__title-block {
		display: block;
		padding-right: 4px;
	}

	.db-games-t-i-col__value-block {
		width: auto;
	}

	.db-games-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-games-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-games-t-item {
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-games-t-item:first-child {
		margin-top: 0;
	}

	.db-page-left {
		display: none;
	}

	.db-page-right {
		width: 100%;
		padding-left: 0;
	}

	.db-page--wide-menu .db-page-right {
		width: 100%;
	}

	.dashboard-tab {
		padding: 13px 7px;
		width: 100%;
		justify-content: center;
	}

	.dashboard-tab-wrapper {
		width: 25%;
	}

	.db-page-topline-panel__right__content .topline-logout-btn-block {
		display: none;
	}

	.db-page-topline {
		background-color: transparent;
		padding: 0;
		border-radius: 0;
	}

	.db-page-topline-panel {
		flex-direction: column-reverse;
	}

	.db-page-topline-panel__left {
		width: 100%;
		justify-content: space-between;
		background: #14202d;
		border-radius: 8px;
		padding: 8px;
		margin-top: 4px;
	}

	.db-page-topline-panel__right {
		width: 100%;
		justify-content: space-between;
		background: #14202d;
		border-radius: 8px;
		padding: 8px;
	}

	.db-page-topline-panel__right__logo {
		display: block;
	}

	.db-page-content-block {
		padding-top: 4px;
	}

	.db-balance-t-h-col--payments,
	.db-balance-t-i-col--payments {
		width: calc(35% - 32px);
	}

	.db-balance-t-h-col--currency,
	.db-balance-t-i-col--currency {
		width: calc(32% - 32px);
	}

	.db-balance-t-h-col--balance,
	.db-balance-t-i-col--balance {
		width: calc(32% - 32px);
	}

	.db-balance-t-h-col--actions,
	.db-balance-t-i-col--actions {
		width: 96px;
	}

	.dashboard-right {
		padding-top: 4px;
	}

	.dashboard-tabs-content-block {
		padding-top: 4px;
	}

	.new-deposit-info-stat-item-wrapper {
		width: 100%;
	}

	.new-deposit-step-list-item__title {
		display: none;
	}

	.new-deposit-step-list-item.active .new-deposit-step-list-item__title {
		display: block;
	}

	.new-deposit-step-list-item.ready .new-deposit-step-list-item__count {
		opacity: 1;
		visibility: visible;
	}

	.new-deposit-step-list-item.ready .new-deposit-step-list-item__count-block {
		width: 56px;
		padding-left: 32px;
	}

	.deposits-filter-panel-btn-block {
		display: block;
	}

	.deposits-filter-panel {
		display: none;
	}

	.deposits-filter-form-col {
		width: 100%;
	}

	.withdrawal-content-block {
		padding: 12px;
	}

	.transactions-filter-panel-btn-block {
		display: block;
	}

	.transactions-filter-panel {
		display: none;
	}

	.transactions-filter-form-col {
		width: 100%;
	}

	.db-side-tablet-block {
		display: none;
	}


	.db-affiliate-program-t-heading {
		display: none;
	}


	.db-affiliate-program-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-affiliate-program-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-affiliate-program-t-item:first-child {
		margin-top: 0;
	}

	.db-affiliate-program-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-affiliate-program-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-affiliate-program-t-i-col__title-block {
		display: block;
	}

	.db-affiliate-program-t-i-col__value-block {
		width: auto;
	}

	.db-affiliate-program-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-affiliate-program-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}



	.db-affiliate-program-t-item.active:nth-child(1)::before,
	.db-affiliate-program-t-item.active:nth-child(2)::before {
		width: 100%;
	}

	.db-affiliate-program-t-item.active:nth-child(3)::before,
	.db-affiliate-program-t-item.active:nth-child(4)::before,
	.db-affiliate-program-t-item.active:nth-child(5)::before {
		width: 100%;
	}

	.db-affiliate-program-t-item.active:nth-child(6)::before,
	.db-affiliate-program-t-item.active:nth-child(7)::before {
		width: 100%;
	}

	.db-affiliate-program-t-item.active:nth-child(8)::before,
	.db-affiliate-program-t-item.active:nth-child(9)::before,
	.db-affiliate-program-t-item.active:nth-child(10)::before {
		width: 100%;
	}




	.db-affiliate-bonus-t-heading {
		display: none;
	}


	.db-affiliate-bonus-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-affiliate-bonus-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-affiliate-bonus-t-item:first-child {
		margin-top: 0;
	}

	.db-affiliate-bonus-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-affiliate-bonus-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-affiliate-bonus-t-i-col__title-block {
		display: block;
	}

	.db-affiliate-bonus-t-i-col__value-block {
		width: auto;
	}

	.db-affiliate-bonus-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-affiliate-bonus-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-affiliate-program-top {
		flex-wrap: wrap;
	}

	.db-affiliate-program-top-left {
		width: 100%;
	}

	.db-affiliate-program-top-right {
		padding-top: 16px;
		width: auto;
	}

	.db-affiliate-tab__title {
		display: none;
	}

	.db-affiliate-tabs {
		justify-content: space-between;
	}

	.db-affiliate-tab-wrapper {
		/* width: 25%; */
		flex-grow: 1;
	}

	.db-affiliate-tab.active .db-affiliate-tab__title {
		display: block;
	}

	.modal--partner .modal-dialog {
		max-width: 516px;
	}

	.m-partner-t-i-col--games,
	.m-partner-t-i-col--lotteries,
	.m-partner-t-i-col--stacking {
		justify-content: flex-end;
	}

	.m-partner-t-i-col__value-block {
		width: auto;
	}


	.setting-tab__title {
		display: none;
	}

	.setting-tabs {
		justify-content: space-between;
	}

	.setting-tab-wrapper {
		/* width: 25%; */
		flex-grow: 1;
	}

	.setting-tab.active .setting-tab__title {
		display: block;
	}

	.front-top-slide-item-image-block {
	}

	.front-top-slide-item-image {
		width: 140px;
		height: 120px;
		right: 24px;
		bottom: 24px;
	}

	.front-top-slide-item-image-diamond {
		width: 47px;
		height: 47px;
		right: auto;
		top: 57px;
		left: 55px;
	}

	.front-top-feature {
		padding: 16px 8px;
	}

	.front-top-feature__arrow {
		right: 8px;
	}

	.front-top-feature__content {
		padding-right: 16px;
	}

	.front-top-feature__soon-label-block {
		top: 8px;
		right: 8px;
	}

	.front-staking__image {
		right: -270px;
		bottom: -270px;
	}

	.footer-content-left .footer-copy {
		display: none;
	}

	.footer-content-right .footer-copy {
		display: block;
	}

	.footer-content-right {
		padding-top: 24px;
	}

	.footer-menu-blocks {
		flex-direction: row-reverse;
	}

	.footer-menu-block {
		width: 50%;
	}

	.footer-contact-items-block {
		margin-top: 12px;
		max-width: 220px;
	}

	.about-us-vision {
		padding: 24px;
	}

	.about-us-vision__descr {
		margin-top: 24px;
		font-size: 16px;
	}

	.about-us-vision__btn-block {
		margin-top: 24px;
		position: relative;
		bottom: 0;
		left: 0;
	}

	.about-us-values {
		padding: 24px;
	}

	.about-us-values__descr {
		font-size: 16px;
	}

	.section-descr {
		font-size: 16px;
	}

	.about-community {
		padding: 24px;
	}

	.about-community-descr {
		font-size: 16px;
	}

	.staking-benefit-item-wrapper {
		width: 100%;
	}

	.staking-benefit-item__text {
		max-width: 350px;
	}

	.staking-benefit-item {
		align-items: center;
	}

	.affiliate-program-tabs-pabel {
		position: relative;
		top: auto;
	}

	.affiliate-tab-content .db-affiliate-program-warning-block {
		margin-top: 12px;
	}

	.affiliate-tab {
		padding-left: 7px;
		padding-right: 7px;
	}

	.news-item-wrapper {
		width: 100%;
	}

	.contacts {
		flex-direction: column-reverse;
	}

	.contacts-right {
		width: 100%;
	}

	.contacts-left {
		width: 100%;
	}

	.contacts-form-block {
		border: 1px solid #1a314b;
		border-radius: 24px 24px 0 0;
	}

	.contacts-info-block {
		border: 1px solid #1a314b;
		border-radius: 0 0 24px 24px;
	}

	.faq-top-ask {
		flex-wrap: wrap;
	}

	.faq-top-ask-left {
		width: 100%;
		padding-right: 0;
	}

	.faq-top-ask-right {
		width: 100%;
		padding-top: 12px;
		padding-left: 32px;
	}

	.faq-top-ask-btn {
		width: auto;
	}

	.promotion-item-wrapper {
		width: 50%;
	}

	.promotions-filter-form-row {
		flex-wrap: wrap;
	}

	.promotions-filter-form-col--left {
		width: 100%;
		max-width: 100%;
	}

	.promotions-filter-form-col--right {
		width: 100%;
		padding-top: 12px;
		display: flex;
	}

	.promotions-filter-form-col--right .bootstrap-select>.dropdown-menu {
		inset: 0px auto auto 0px !important;
	}

	.promotions-filter-form-type-item {
		padding-left: 10px;
		padding-right: 10px;
	}

	.modal--promotion .modal-dialog {
		max-width: 516px;
	}

	.modal--promotion .modal-content {
		padding: 48px;
	}

	.db-affiliate-promo-top {
		flex-wrap: wrap;
	}

	.db-affiliate-promo-top-left {
		width: 100%;
	}

	.db-affiliate-promo-top-right {
		padding-top: 16px;
		width: auto;
	}

	.deposits-empty-item-wrapper {
		width: 100%;
	}

	.deposits-empty-item {
		display: flex;
		align-items: center;
	}

	.deposits-empty-item__descr {
		width: calc(100% - 40px);
		padding-top: 0;
		padding-left: 16px;
		text-align: left;
	}

	.db-empty-elements-block {
		min-height: 320px;
	}

	.cookie-block {
		max-width: 516px;
	}

	.cookie {
		flex-direction: column;
		align-items: center;
	}

	.cookie__left {
		padding-right: 0;
		padding-bottom: 16px;
	}

	.cookie__info {
		flex-direction: column;
		flex-wrap: wrap;
	}

	.cookie__info__icon {
		margin-right: auto;
		margin-left: auto;
	}

	.cookie__info__descr {
		padding-left: 0;
		margin-top: 12px;
		text-align: center;
		letter-spacing: 0;
	}


	.games-bet-table-block .db-empty-elements-block {
		min-height: 0;
		padding: 0;
		padding-top: 64px;
		padding-bottom: 64px;
	}

	.game-bet-slider-block {
		width: 230px;
	}

	.available-currency-list-block {
		display: none;
	}

	.available-currency-slider-block {
		display: block;
	}

	.game-dice-slider-block {
		padding: 8px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-grid-text {
		top: -60px;
	}

	.game-dice-bet {
		width: 36px;
		height: 40px;
		background-size: 36px 40px;
		bottom: 0;
	}

	.game-dice-bet__count {
		font-size: 12px;
	}

	.game-panel--dice .game-panel__middle {
		padding-top: 36px;
		padding-bottom: 36px;
	}

}




/* xs <= Small (sm) */
@media (max-width : 575.98px) {
	.topline-login-btn-block {
		display: none;
	}

	.topline-registration-btn-block {
		display: none;
	}

	.registration-right {
		padding: 12px;
	}

	.registration-mobile-feature-item-wrapper {
		width: 100%;
	}

	.custom-modal .modal-content {
		padding: 24px 12px;
	}

	.lottery-list-item-wrapper {
		width: 100%;
	}

	.lottery-list-filter-form-col--type {
		width: 100%;
	}

	.lottery-list-filter-form-col--currency {
		width: 100%;
	}

	.lottery-list-filter-form-price-row {
		flex-wrap: wrap;
	}

	.lottery-list-filter-form-price-col--title {
		width: 100%;
	}

	.lottery-list-filter-form-price-title {
		text-align: left;
	}

	.lottery-list-filter-form-price-title br {
		display: none;
	}

	.lottery-list-filter-form-price-col--field {
		width: 50%;
		padding-top: 8px;
	}

	.lottery-list-filter-form-fund-row {
		flex-wrap: wrap;
	}

	.lottery-list-filter-form-fund-col--title {
		width: 100%;
	}

	.lottery-list-filter-form-fund-title {
		text-align: left;
	}

	.lottery-list-filter-form-fund-title br {
		display: none;
	}

	.lottery-list-filter-form-fund-col--field {
		width: 100%;
		padding-top: 8px;
	}

	.topline .topline-dashboard-btn-block {
		display: none;
	}

	.topline .topline-logout-btn-block {
		display: none;
	}

	.lottery-list-top-right {
		width: 100%;
	}

	.lottery-list-items-empty {
		padding: 40px;
	}

	.lottery-detail-info {
		padding: 16px;
	}

	.lottery-detail-info-top {
		flex-wrap: wrap;
	}

	.lottery-detail-info-top__left {
		width: 100%;
		padding-right: 0;
	}

	.lottery-detail-info-top__right {
		width: 100%;
		padding-top: 12px;
	}

	.lottery-detail-info-creator__title {
		text-align: left;
	}

	.lottery-detail-info-creator__content {
		justify-content: flex-start;
	}

	.lottery-detail-info-item__value {
		flex-direction: column-reverse;
	}

	.lottery-detail-info-item {
		padding: 8px 4px;
	}

	.lottery-detail-info-items {
		margin-top: -4px;
		margin-left: -2px;
		margin-right: -2px;
	}

	.lottery-detail-info-item-wrapper {
		padding-left: 2px;
		padding-right: 2px;
		margin-top: 4px;
	}

	.lottery-detail-info-item__title {
		margin-top: 16px;
	}

	.lottery-detail-info-item__value__icon {
		width: 20px;
		height: 20px;
	}

	.lottery-detail-info-item__value__text {
		font-size: 20px;
	}

	.lottery-detail-info-item--prize .lottery-detail-info-item__value__text {
		max-width: none;
		padding-right: 0;
		padding-top: 8px;
	}

	.lottery-detail-info-item--price .lottery-detail-info-item__value__text {
		max-width: none;
		padding-right: 0;
		padding-top: 8px;
	}

	.lottery-detail-info-purchase-form-row {
		flex-wrap: wrap;
	}

	.lottery-detail-info-purchase-form__field {
		width: 100%;
		padding-right: 0;
	}

	.lottery-detail-info-purchase-form__btn-block {
		width: 100%;
		padding-left: 0;
		padding-top: 16px;
	}

	.lottery-detail-ticket-t-i-tickets-tooltip {
		min-width: 86px;
		max-width: 86px;
	}

	.related-lottery-slider-block {
		margin-top: 24px;
		padding-top: 76px;
	}

	.related-lottery-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
	}

	.related-lottery-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
	}

	.lottery-detail-download {
		padding: 8px;
	}

	.lottery-detail-guarantee-btn {
		padding: 8px;
	}

	.lottery-create-banner {
		padding: 32px 16px;
		padding-bottom: 260px;
	}

	.lottery-create-banner-bg-image {
		right: auto;
		left: 50%;
		transform: translateX(-50%);
		bottom: -40px;
		top: auto;
	}

	.lottery-create-banner-info-slider-block {
		margin-left: auto;
	}

	.lottery-create-banner-info-slide-title {
		text-align: center;
	}

	.lottery-create-banner-info-slider .swiper-pagination {
		justify-content: center;
	}

	.lottery-create-block {
		padding: 12px;
		padding-bottom: 18px;
	}

	.games-list-filter-form-col--type {
		width: 100%;
		order: 1;
	}

	.games-list-filter-form-col--search {
		width: 100%;
		order: 2;
	}

	.games-list-filter-form-col--category {
		width: 100%;
		order: 3;
	}

	.games-list-items-block {
		margin-top: 12px;
		padding: 12px;
	}

	.games-list-items {
		margin-left: -6px;
		margin-right: -6px;
	}

	.games-list-item-wrapper {
		width: 50%;
		margin-top: 12px;
		padding-left: 6px;
		padding-right: 6px;
	}

	.games-bet-tabs-block {
		padding-top: 1px;
		padding-bottom: 1px;
	}

	.games-bet-tabs {
		flex-wrap: wrap;
		margin-left: -4px;
		margin-right: -4px;
		margin-top: -4px;
		justify-content: space-between;
	}

	.games-bet-tab-wrapper {
		padding-left: 4px;
		padding-right: 4px;
		margin-top: 4px;
	}

	.games-bet-tab {
		padding-left: 2px;
		padding-right: 2px;
		justify-content: center;
	}

	.games-bet-tab-wrapper:nth-child(1),
	.games-bet-tab-wrapper:nth-child(2) {
		width: 50%;
	}

	.games-bet-tab__icon {
		display: none;
	}

	.games-bet-tab__icon+.games-bet-tab__title {
		padding-left: 0;
	}

	.games-bet-t-i-user {
		flex-direction: row-reverse;
	}

	.games-bet-t-i-user__icon+.games-bet-t-i-user__username {
		padding-left: 0;
		padding-right: 8px;
	}


	.games-wins-t-i-user {
		flex-direction: row-reverse;
	}

	.games-wins-t-i-user__icon+.games-wins-t-i-user__username {
		padding-left: 0;
		padding-right: 8px;
	}

	.game-roulette__cylinder {
		width: 270px;
		height: 270px;
	}

	.game-roulette__cylinder__stator {
		width: 326px;
		height: 326px;
	}

	.game-roulette__cylinder__rotor {
		width: 90px;
		height: 90px;
	}

	.game-roulette-bet-btn {
		width: 16px;
		height: 16px;
		font-size: 6px;
		border-radius: 3px;
	}

	.game-roulette-bet-left {
		max-width: 18px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-right {
		max-width: 18px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center-left {
		max-width: 72px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-center {
		max-width: 72px;
		padding-left: 1px;
		padding-right: 1px;
	}



	.game-roulette-bet-center-right {
		max-width: 72px;
		padding-left: 1px;
		padding-right: 1px;
	}

	.game-roulette-bet-btn--zero {
		height: 52px;
	}

	.game-roulette-bet-btn--long {
		width: 70px;
	}

	.game-roulette-bet-btn--middle {
		width: 34px;
	}

	.chip-value-slider .swiper-button-prev {
		left: -14px;
	}

	.chip-value-slider .swiper-button-next {
		right: -14px;
	}

	.game-descr-tab-wrapper {
		width: 50%;
	}

	.game-descr-tab {
		padding-left: 4px;
		padding-right: 4px;
		justify-content: center;
	}

	.game-descr-tabs-wrapper {
		width: 100%;
	}

	.game-panel--dice .game-panel__middle {
		padding-top: 60px;
		padding-bottom: 60px;
	}

	.game-dice-fields-block {
		padding: 6px;
	}

	.game-dice-fields-row {
		flex-wrap: wrap;
		margin-left: -2px;
		margin-right: -2px;
	}

	.game-dice-fields-col {
		padding-left: 2px;
		padding-right: 2px;
		/* width: 100%; */
	}

	.game-dice-fields-col .field-right-panel-block {
		right: 4px;
	}

	.game-dice-fields-col .field input[type='text'],
	.game-dice-fields-col .field input[type='email'],
	.game-dice-fields-col .field input[type='password'] {
		padding-left: 4px;
		padding-right: 24px;
	}

	.game-panel--limbo .game-panel__middle {
		padding-top: 62px;
		padding-bottom: 62px;
	}

	.game-limbo-fields-col {
		width: 100%;
	}

	.dashboard-info-stat-field-wrapper--total {
		width: 100%;
	}

	.dashboard-info-stat-field-wrapper--stacking {
		width: 100%;
	}

	.dashboard-info-panel__bottom {
		width: 100%;
	}

	.dashboard-tab__icon+.dashboard-tab__title {
		display: none;
	}

	.db-balance-top-right {
		display: none;
	}

	.db-balance-top-left {
		width: 100%;
		padding-right: 0;
	}



	.db-balance-t-heading {
		display: none;
	}


	.db-balance-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.db-balance-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.db-balance-t-item:first-child {
		margin-top: 0;
	}

	.db-balance-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.db-balance-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.db-balance-t-i-col__title-block {
		display: block;
	}

	.db-balance-t-i-col__value-block {
		width: auto;
	}

	.db-balance-t-i-payment {
		flex-direction: row-reverse;
	}

	.db-balance-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-lottery-top {
		flex-wrap: wrap;
	}

	.db-lottery-top-left {
		width: 100%;
		padding-right: 0;
	}

	.db-lottery-top-right {
		width: 100%;
		padding-top: 12px;
	}

	.db-lottery-top-create-lottery-btn {
		width: 100%;
	}


	.db-games-top {
		flex-wrap: wrap;
	}

	.db-games-top-left {
		width: 100%;
		padding-right: 0;
	}

	.db-games-top-right {
		width: 100%;
		padding-top: 12px;
	}

	.db-games-top-create-lottery-btn {
		width: 100%;
	}

	.db-deposits-top {
		flex-wrap: wrap;
	}

	.db-deposits-top-left {
		width: 100%;
		padding-right: 0;
	}

	.db-deposits-top-right {
		width: 100%;
		padding-top: 12px;
	}

	.db-deposits-top-create-lottery-btn {
		width: 100%;
	}

	.modal-info__content {
		padding-left: 12px;
	}

	.modal--info .modal-content {
		padding: 24px;
		padding-top: 32px;
	}

	.modal--info .modal-body-content {
		padding-right: 0;
	}

	.modal--info .modal-close {
		right: 12px;
	}

	.modal-info__icon-block {
		width: 40px;
		height: 40px;
		padding-bottom: 5px;
		background-size: 40px 40px;
		margin-right: auto;
		margin-left: auto;
	}

	.modal-info__icon {
		width: 16px;
		height: 16px;
	}

	.modal-info {
		display: block;
	}

	.modal-info__content {
		width: 100%;
		padding-left: 0;
		padding-top: 12px;
		text-align: center;
	}

	.new-deposit-step-list-item {
		padding-left: 11px;
		padding-right: 11px;
	}

	.new-deposit-step-list-item.active .new-deposit-step-list-item__title {
		display: none;
	}

	.new-dep-ps-radio-item-wrapper {
		width: 100%;
	}

	.new-dep-inv-plan-radio-item-wrapper {
		width: 100%;
	}

	.modal--deposit .modal-content {
		padding: 24px 12px;
	}

	.refill-content-block {
		padding-top: 12px;
	}

	.withdrawal-form-bottom {
		flex-direction: column-reverse;
	}

	.withdrawal-form-bottom-left {
		width: 100%;
		padding-top: 16px;
	}

	.withdrawal-form-bottom-right {
		width: 100%;
		padding-left: 0;
	}

	.confirmation-code-fields-block {
		padding-top: 0;
		padding-bottom: 0;
	}

	.confirmation-code-field {
		max-width: 40px;
		margin-right: auto;
		margin-left: auto;
	}

	.confirmation-code-field .field input[type='text'],
	.confirmation-code-field .field input[type='email'],
	.confirmation-code-field .field input[type='password'] {
		height: 40px;
	}

	.transactions-empty-empty {
		height: 500px;
	}

	.db-affiliate-program-top-right {
		width: 100%;
	}

	.db-affiliate-program-tabs-block {
		width: 100%;
	}

	.db-affiliate-program-tab-wrapper {
		width: 50%;
	}

	.db-affiliate-tab.active .db-affiliate-tab__title {
		display: none;
	}

	.db-affiliate-tab-wrapper {
		width: 25%;
		flex-grow: 0;
	}





	.m-partner-t-heading {
		display: none;
	}


	.m-partner-t-item {
		display: block;
		position: relative;
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #1a314b;
		margin-top: 12px;
	}

	.m-partner-t-item:nth-child(odd) {
		background-color: transparent;
	}

	.m-partner-t-item:first-child {
		margin-top: 0;
	}

	.m-partner-t-i-col {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 15px 12px;
	}

	.m-partner-t-i-col:nth-child(odd) {
		background: #0e1b28;
	}

	.m-partner-t-i-col__title-block {
		display: block;
	}

	.m-partner-t-i-col__value-block {
		width: auto;
	}

	.m-partner-t-i-payment {
		flex-direction: row-reverse;
	}

	.m-partner-t-i-payment__title {
		padding-left: 0;
		padding-right: 8px;
	}

	.db-affiliate-partners-top-right {
		display: none;
	}

	.db-affiliate-partners-top-left {
		width: 100%;
		padding-right: 0;
	}

	.setting-tab.active .setting-tab__title {
		display: none;
	}


	.db-wallets-top-right {
		display: none;
	}

	.db-wallets-top-left {
		width: 100%;
		padding-right: 0;
	}

	.db-autorisations-top-right {
		display: none;
	}

	.db-autorisations-top-left {
		width: 100%;
		padding-right: 0;
	}

	.modal--add-wallet .modal-content,
	.modal--edit-wallet .modal-content {
		padding-top: 24px;
		padding-bottom: 24px;
	}

	.front-top-feature-wrapper {
		width: 100%;
	}

	.front-top-slide-item {
		padding: 16px;
		padding-top: 32px;
		padding-bottom: 340px;
		align-items: flex-start;
	}

	.front-top-slide-item-count-block {
		display: none;
	}

	.front-top-slide-item-count-block--mobile {
		display: block;
	}

	.front-top-slide-item-label-block {
		margin-top: 16px;
	}

	.front-top-slide-item-image-block {
	}

	.front-top-slide-item-image {
		width: 236px;
		height: 200px;
		right: auto;
		left: 50%;
		bottom: 48px;
		transform: translateX(-50%);
	}


	.front-top-slider .swiper-button-prev {
		bottom: 280px;
		left: 16px;
		top: auto;
		transform: none;
	}

	.front-top-slider .swiper-button-next {
		bottom: 280px;
		left: 65px;
		top: auto;
		transform: none;
	}

	.s-front-section-top {
		padding-top: 0;
		padding-bottom: 0;
		padding-right: 0;
	}

	.s-front-section-top-left {
		width: 100%;
		justify-content: space-between;
	}

	.front-staking {
		padding: 16px;
		padding-bottom: 154px;
	}

	.front-staking__image {
		right: auto;
		left: -30px;
		bottom: -290px;
	}


	.promotion-slider-block {
		margin-top: 24px;
		padding-top: 76px;
	}

	.promotion-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
		top: -76px;
	}

	.promotion-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
		top: -76px;
	}

	.news-slider-block {
		margin-top: 24px;
		padding-top: 76px;
	}

	.news-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
		top: -76px;
	}

	.news-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
		top: -76px;
	}


	.ns-news-slider-block {
		margin-top: 24px;
		padding-top: 76px;
	}

	.ns-news-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
		top: -76px;
	}

	.ns-news-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
		top: -76px;
	}

	.start-earning-content {
		padding: 64px 32px;
	}

	.start-earning-image-left {
		width: 173px;
		left: -87px;
		top: -77px;
	}

	.start-earning-image-right {
		width: 198px;
		right: -82px;
		bottom: -98px;
	}

	.start-earning-btn {
		padding-left: 24px;
		padding-right: 24px;
	}

	.footer-menu-block {
		width: 100%;
	}

	.footer-menu-block {
		margin-top: 24px;
	}

	.footer-menu-blocks {
		margin-top: -24px;
		flex-direction: column-reverse;
	}

	.about-offer-item-wrapper {
		width: 100%;
	}

	.staking-offer-top {
		flex-direction: column;
		align-items: flex-start;
	}

	.staking-offer-top-right {
		padding-top: 16px;
	}

	.staking-offer-item-wrapper {
		width: 100%;
	}

	.calculator {
		padding: 16px;
	}

	.calculator-result-item-wrapper {
		width: 100%;
	}

	.contacts-form-block {
		padding: 24px;
	}

	.contacts-info-block {
		padding: 24px;
	}

	.contact-email-link__text {
		font-size: 14px;
	}

	.contact-phone-link__text {
		font-size: 14px;
	}

	.contact-address__text {
		font-size: 14px;
	}

	.section-faq+.section-start-earning .start-earning-image-right {
		right: -82px;
	}

	.section-news-single+.section-start-earning .start-earning-image-right {
		right: -82px;
	}

	.faq-top-ask-btn {
		width: 100%;
	}

	.faq-category-item-wrapper {
		width: 100%;
	}

	.content-image-list-item-wrapper {
		width: 100%;
	}

	.modal--image .modal-content {
		padding-top: 24px;
	}



	.hot-promo-slider-block {
		margin-top: 24px;
		padding-top: 76px;
	}

	.hot-promo-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
		top: -76px;
	}

	.hot-promo-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
		top: -76px;
	}

	.promotion-item__bottom-block {
		position: relative;
		bottom: auto;
		left: auto;
		right: auto;
	}

	.promotion-item__content {
		height: auto;
		padding-bottom: 24px;
	}

	.promotion-item-wrapper {
		width: 100%;
	}

	.promotions-filter-form-type-items {
		flex-wrap: wrap;
		justify-content: center;
	}

	.promotions-filter-form-type-items-block {
		padding: 4px;
	}

	.modal--promotion .modal-content {
		padding: 24px;
	}

	.news-single-community-blockquote-wrapper {
		width: 100%;
	}


	.db-affiliate-banners-top-right {
		display: none;
	}

	.db-affiliate-banners-top-left {
		width: 100%;
		padding-right: 0;
	}



	.db-affiliate-promo-pdf-top-right {
		display: none;
	}

	.db-affiliate-promo-pdf-top-left {
		width: 100%;
		padding-right: 0;
	}


	.db-empty-elements-block {
		min-height: 190px;
	}

	.no-partners-benefit-item-wrapper {
		width: 100%;
	}

	.cookie-block {
		max-width: 296px;
	}

	.game-dice-bet {
		width: 30px;
		height: 33px;
		background-size: 30px 33px;
		bottom: 0;
	}

	.game-dice-bet__count {
		font-size: 10px;
	}

	.game-bet-slide-item {
		font-size: 12px;
		line-height: 1.5;
		width: 52px;
		height: 24px;
		padding: 2px;
	}

	.game-bet-slider-block {
		width: 212px;
		margin-top: 8px;
	}


	.available-currency-slider-block {
		padding-bottom: 44px;
	}

	.available-currency-slider {
		padding-right: 2px;
	}

	.available-currency-slider .swiper-button-prev {
		right: 50%;
		margin-right: 4px;
		top: auto;
		bottom: -44px;
		transform: none;
	}

	.available-currency-slider .swiper-button-next {
		right: auto;
		left: 50%;
		margin-left: 4px;
		top: auto;
		bottom: -44px;
		transform: none;
	}

	.section-privacy {
		padding-top: 32px;
		padding-bottom: 28px;
	}

	.field--game-seed textarea {
		padding-top: 36px;
		padding-bottom: 50px;
		padding-left: 8px;
		padding-right: 8px;
	}

	.field--game-seed .field-icon {
		transform: none;
		top: 8px;
		left: 8px;
	}

	.field-block--game-seed .field-right-panel-block {
		top: auto;
		transform: none;
		left: 0;
		bottom: 8px;
		right: auto;
	}

	.field-block--game-seed .copy-field-btn {
		width: 32px;
		height: 32px;
	}

	.field--game-seed .copy-field-btn::before {
		width: 20px;
		height: 20px;
		mask-size: 20px 20px;
		-webkit-mask-size: 20px 20px;
	}

	.field-block--game-seed .download-field-btn {
		width: 32px;
		height: 32px;
	}

	.field--game-seed .download-field-btn::before {
		width: 20px;
		height: 20px;
		mask-size: 20px 20px;
		-webkit-mask-size: 20px 20px;
	}

	.db-address-whitelist-top-right {
		display: none;
	}

	.db-address-whitelist-top-left {
		width: 100%;
		padding-right: 0;
	}




	.game-dice-slider-block {
		padding: 8px;
	}

	.game-dice-slider-wrapper {
		padding: 6px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-grid-text {
		font-size: 12px;
		top: -36px;
	}

	.game-dice-slider-wrapper .irs--flat {
		height: 6px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-line {
		height: 6px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-bar--single {
		height: 6px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-grid {
		height: 6px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-handle {
		height: 24px;
		width: 24px;
		padding: 6px;
		border-radius: 4px;
		box-shadow: 0 2px 5px -2px rgba(4, 121, 226, 0.27);
		top: -9px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-handle::before {
		mask-size: 14px 14px;
		-webkit-mask-size: 14px 14px;
		width: 14px;
		height: 14px;
	}

	.game-dice-block {
		margin-top: 24px;
	}

	.game-dice-slider-wrapper .irs--flat .irs-grid-text::before {
		top: 14px;
	}


}


/* === MOBILE FIRST === */

/* Custom (xs) */
@media (min-width : 0) {}

/* Small (sm) */
@media (min-width : 576px) {}

/* Medium (md) */
@media (min-width : 768px) {}

/* Large (lg) */
@media (min-width : 992px) {}

/* Extra large (xl) */
@media (min-width : 1200px) {}

/* Extra extra large (xxl) */
@media (min-width : 1400px) {}

@media (min-width: 241px) and (max-width: 480px) {
	#toast-container>div {
		padding: 8px 8px 8px 50px;
		width: 300px;
	}

	#toast-container .toast-close-button {
		right: 16px;
		top: 20px;
	}

	.toast-bottom-right {
		right: 10px;
		bottom: 10px;
	}
}