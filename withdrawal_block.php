<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();

// Original withdrawal logic preserved
$type = (isset($_GET['type']) && in_array($_GET['type'], array(1,2,3,4,5,6,7,8,9,10, 11))) ? $_GET['type'] : 1;
$typearr = array(10=>'USDT', 11=>'USDT');
$type2 = $typearr[$type];
$title = "Withdrawal ".$type2;
$wallet_field = ($type == 11) ? 'wallet_promo' : 'wallet';
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Withdrawal Styling */
.withdrawal-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.withdrawal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

/* Wallet Balance Card */
.wallet-balance-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.wallet-balance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wallet-balance-card:hover::before {
    opacity: 1;
}

.wallet-balance-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.wallet-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
    color: #fff;
}

.wallet-amount {
    font-size: 24px;
    font-weight: 600;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.wallet-label {
    color: #848e9c;
    font-size: 14px;
    font-weight: 500;
}

.stat-change {
    color: #848e9c;
    font-size: 12px;
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.stat-change i {
    margin-right: 5px;
    color: #17c964;
}

/* Withdrawal Card */
.withdrawal-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.withdrawal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-body {
    padding: 30px;
}

/* Form Styling */
.form-group {
    margin-bottom: 25px;
}

.form-label {
    color: #848e9c;
    margin-bottom: 10px;
    font-weight: 500;
    display: block;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #eaecef;
    padding: 12px 15px;
    width: 100%;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(23, 201, 100, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(23, 201, 100, 0.25);
    color: #fff;
    outline: none;
}

.form-control::placeholder {
    color: #848e9c;
}

/* Input Validation */
.form-control.is-invalid {
    border-color: #f6465d;
    background: rgba(246, 70, 93, 0.1);
}

.form-control.is-valid {
    border-color: #17c964;
    background: rgba(23, 201, 100, 0.1);
}

.invalid-feedback {
    color: #f6465d;
    font-size: 12px;
    margin-top: 5px;
}

.valid-feedback {
    color: #17c964;
    font-size: 12px;
    margin-top: 5px;
}

.btn-withdrawal {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    border: none;
    border-radius: 6px;
    color: #fff;
    font-weight: 600;
    padding: 12px 25px;
    width: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-withdrawal:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(23, 201, 100, 0.3);
    color: #fff;
}

.btn-withdrawal:active {
    transform: translateY(0);
}

.btn-withdrawal i {
    margin-right: 8px;
}

.balance-info {
    background: rgba(23, 201, 100, 0.1);
    border: 1px solid rgba(23, 201, 100, 0.2);
    border-radius: 6px;
    padding: 10px 15px;
    margin-top: 8px;
    color: #17c964;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.balance-info i {
    margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .withdrawal-header {
        padding: 20px;
    }

    .wallet-balance-card {
        padding: 15px;
    }

    .card-body {
        padding: 20px;
    }

    .wallet-amount {
        font-size: 20px;
    }

    .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 480px) {
    .wallet-amount {
        font-size: 18px;
    }

    .btn-withdrawal {
        padding: 15px 20px;
        font-size: 16px;
    }
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
        <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Dashboard</div>
          </div>
          </a> </li>
        <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
           <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-icon"><img src="assets/network.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
           <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy" >Matching Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
        </div>
        <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
       </div>

           <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings active">
          <div class="mobile-db-menu-icon"><img src="assets/withdraw.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
        </div>

          <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="topline-logout-btn__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Logout</div>
          </div>
          </a> </li>
      </ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>

<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings active">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
          <!--  <div class="topline-user-panel-block">-->
          <!--    <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"-->
										<!--aria-expanded="false">-->
          <!--      <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--      <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>-->
          <!--      </a>-->
          <!--      <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Profile</div>-->
          <!--        </a> <a href="logout.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Logout</div>-->
          <!--        </a> </div>-->
          <!--    </div>-->
          <!--  </div>-->
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">Withdraw your funds securely to your wallet address. Please ensure all details are correct before submitting.</div>

                          <!-- Wallet Balance Card -->
                          <div class="wallet-balance-card">
                              <div class="wallet-icon">
                                  <i class="fas fa-wallet"></i>
                              </div>
                              <div class="wallet-label"><?php echo ($type == 11) ? 'Working Wallet Balance' : 'Wallet Balance';?></div>
                              <div class="wallet-amount"><?php echo SITE_CURRENCY; ?><?php echo number_format($user->$wallet_field*1, 2);?></div>
                              <div class="stat-change">
                                  <i class="fas fa-coins"></i>
                                  Available for Withdrawal
                              </div>
                          </div>

                          <!-- Withdrawal Information -->
                          <div class="withdrawal-info">
                              <h4 style="color : white"><i class="fas fa-info-circle"></i> Withdrawal Information</h4>
                              <ul>
                                  <li style="color : white">Minimum withdrawal amount: 10 <?php echo $type2; ?></li>
                                  <li style="color : white">Processing time: 24-48 hours</li>
                                  <li style="color : white">Network fees may apply</li>
                                  <li style="color : white">Ensure your wallet address is correct</li>
                              </ul>
                          </div>

                          <!-- Withdrawal Form -->
                          <div class="withdrawal-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-hand-holding-usd"></i> Request Withdrawal</h3>
                              </div>
                              <div class="card-body">
                                  <form action="withdrawal_block_model.php" method="post" id="withdrawalForm" onSubmit="return abc_();">
                                      <!-- Amount Field -->
                                      <div class="form-group">
                                          <label for="amount" class="form-label"><?php echo $type2;?> Amount *</label>
                                          <input class="form-control" type="number" id="amount" name="amount" value="" step="0.01" min="10" max="<?php echo $user->$wallet_field*1;?>" required placeholder="Enter amount to withdraw">
                                          <div class="balance-info">
                                              <i class="fas fa-info-circle"></i>
                                              Available balance: <?php echo number_format($user->$wallet_field*1, 2);?> <?php echo SITE_CURRENCY;?>
                                          </div>
                                          <div class="invalid-feedback">Please enter a valid withdrawal amount</div>
                                      </div>

                                      <!-- Hidden Type Field -->
                                      <input type="hidden" name="type" value="<?php echo $type;?>" />

                                      <!-- Submit Button -->
                                      <button type="submit" class="btn-withdrawal">
                                          <i class="fas fa-paper-plane"></i> Submit Withdrawal Request
                                      </button>
                                  </form>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Blockchain Scripts (Original Logic Preserved) -->
<?php if(SITE_CURRENCY_ == 'TRX'){?>
<script src="https://cdn.jsdelivr.net/npm/tronweb@2.4.1/dist/TronWeb.node.min.js"></script>
<?php /*<script src="../contract/tron/TronWeb.js"></script>*/?>
<script src="../contract/tron/index.js"></script>
<script src="../contract/tron/login.js"></script>
<?php }elseif(SITE_CURRENCY_ == 'USDT'){?>
<script src="https://cdn.jsdelivr.net/gh/ethereum/web3.js@1.0.0-beta.34/dist/web3.min.js"></script>
<script type="text/javascript" src="../contract/USDT/index.js"></script>
<script type="text/javascript" src="../contract/USDT/login.js"></script>
<script type="text/javascript" src="../contract/USDT/script.js"></script>
<?php }else{?>
<script src="https://cdn.jsdelivr.net/gh/ethereum/web3.js@1.0.0-beta.34/dist/web3.min.js"></script>
<script src="../contract/eth/index.js"></script>
<script src="../contract/eth/login.js"></script>
<?php }?>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Withdrawal page initialized');
        });
    }

    // Initialize the app
    initializeApp();
})();

// Original blockchain authorization function (preserved)
async function abc_(){
    if (typeof web3 !== 'undefined') {
        await ethereum.enable();
        var waddress = await getAccounts();
        if (typeof waddress !== 'undefined' && waddress == '<?php echo $user->USDT_address;?>'){
            return true;
        }
        else{
            alert('Authorization error');
            return false;
        }
    }
    else{
        alert('Authorization error');
        return false;
    }
}

// Enhanced form validation for withdrawal
document.getElementById('withdrawalForm').addEventListener('submit', function(e) {
    const amount = document.getElementById('amount').value;
    const maxAmount = <?php echo $user->$wallet_field*1;?>;
    const minAmount = 10;

    let isValid = true;

    // Validate amount
    if (!amount || isNaN(amount) || parseFloat(amount) < minAmount) {
        document.getElementById('amount').classList.add('is-invalid');
        isValid = false;
    } else if (parseFloat(amount) > maxAmount) {
        document.getElementById('amount').classList.add('is-invalid');
        alert('Insufficient balance. Maximum withdrawal amount is ' + maxAmount + ' <?php echo SITE_CURRENCY;?>');
        isValid = false;
    } else {
        document.getElementById('amount').classList.remove('is-invalid');
        document.getElementById('amount').classList.add('is-valid');
    }

    if (!isValid) {
        e.preventDefault();
        return false;
    }

    // Show confirmation dialog
    const confirmMessage = `Are you sure you want to withdraw ${amount} <?php echo $type2;?>?\n\nThis action cannot be undone.`;
    if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
    }

    return true;
});

// Real-time validation
document.getElementById('amount').addEventListener('input', function() {
    const amount = parseFloat(this.value);
    const maxAmount = <?php echo $user->$wallet_field*1;?>;
    const minAmount = 10;

    if (amount && !isNaN(amount) && amount >= minAmount && amount <= maxAmount) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        if (this.value) {
            this.classList.add('is-invalid');
        }
    }
});

// Add max amount button functionality
function setMaxAmount() {
    const maxAmount = <?php echo $user->$wallet_field*1;?>;
    document.getElementById('amount').value = maxAmount;
    document.getElementById('amount').classList.remove('is-invalid');
    document.getElementById('amount').classList.add('is-valid');
}

// Add quick amount buttons
function setQuickAmount(percentage) {
    const maxAmount = <?php echo $user->$wallet_field*1;?>;
    const amount = (maxAmount * percentage / 100).toFixed(2);
    document.getElementById('amount').value = amount;
    document.getElementById('amount').classList.remove('is-invalid');
    document.getElementById('amount').classList.add('is-valid');
}
</script>

</body>
</html>