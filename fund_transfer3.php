<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();
$title = "Self Transfer";
$fund_type_arr = get_fund_type();
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Self Transfer Styling */
.transfer-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.transfer-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.wallet-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.wallet-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.wallet-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wallet-card:hover::before {
    opacity: 1;
}

.wallet-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.wallet-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
    color: #fff;
}

.wallet-amount {
    font-size: 24px;
    font-weight: 600;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.wallet-label {
    color: #848e9c;
    font-size: 14px;
    font-weight: 500;
}

/* Transfer Form */
.transfer-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.transfer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    color: #848e9c;
    margin-bottom: 10px;
    font-weight: 500;
    display: block;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #eaecef;
    padding: 12px 15px;
    width: 100%;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(23, 201, 100, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(23, 201, 100, 0.25);
    color: #fff;
    outline: none;
}

.form-control::placeholder {
    color: #848e9c;
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23848e9c' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.btn-transfer {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    border: none;
    border-radius: 6px;
    color: #fff;
    font-weight: 600;
    padding: 12px 25px;
    width: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-transfer:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(23, 201, 100, 0.3);
    color: #fff;
}

.btn-transfer:active {
    transform: translateY(0);
}

/* Alert Styling */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: rgba(23, 201, 100, 0.1);
    color: #17c964;
    border-left: 4px solid #17c964;
}

.alert-danger {
    background: rgba(246, 70, 93, 0.1);
    color: #f6465d;
    border-left: 4px solid #f6465d;
}

.alert-info {
    background: rgba(1, 112, 239, 0.1);
    color: #0170ef;
    border-left: 4px solid #0170ef;
}

/* Input Validation */
.form-control.is-invalid {
    border-color: #f6465d;
    background: rgba(246, 70, 93, 0.1);
}

.form-control.is-valid {
    border-color: #17c964;
    background: rgba(23, 201, 100, 0.1);
}

.invalid-feedback {
    color: #f6465d;
    font-size: 12px;
    margin-top: 5px;
}

.valid-feedback {
    color: #17c964;
    font-size: 12px;
    margin-top: 5px;
}

/* User Verification */
.user-verify {
    font-size: 13px;
    margin-top: 5px;
    display: none;
}

.user-verify.success {
    color: #17c964;
    display: block;
}

.user-verify.error {
    color: #f6465d;
    display: block;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    margin-bottom: 0;
    border-radius: 6px 0 0 6px;
}

.input-group-append {
    margin-left: -1px;
    display: flex;
}

.verify-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #17c964;
    padding: 0 15px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 600;
}

.verify-btn:hover {
    background: rgba(23, 201, 100, 0.1);
    border-color: rgba(23, 201, 100, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .transfer-header {
        padding: 20px;
    }

    .wallet-cards {
        grid-template-columns: 1fr;
    }

    .card-body {
        padding: 20px;
    }

    .wallet-card {
        padding: 15px;
    }
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
        <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Dashboard</div>
          </div>
          </a> </li>
        <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
           <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-icon"><img src="assets/network.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
           <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
        </div>
        <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate active">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
       </div>

           <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="mobile-db-menu-icon"><img src="assets/withdraw.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
        </div>

          <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="topline-logout-btn__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Logout</div>
          </div>
          </a> </li>
      </ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>

<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
          <!--  <div class="topline-user-panel-block">-->
          <!--    <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"-->
										<!--aria-expanded="false">-->
          <!--      <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--      <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>-->
          <!--      </a>-->
          <!--      <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Profile</div>-->
          <!--        </a> <a href="logout.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Logout</div>-->
          <!--        </a> </div>-->
          <!--    </div>-->
          <!--  </div>-->
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">Transfer funds between your different wallet types securely and instantly.</div>

                          <!-- Wallet Balance Cards -->
                          <div class="wallet-cards">
                              <div class="wallet-card">
                                  <div class="wallet-icon">
                                      <i class="fas fa-wallet"></i>
                                  </div>
                                  <div class="wallet-label">Spot Wallet</div>
                                  <div class="wallet-amount"><?php echo SITE_CURRENCY; ?><?php echo number_format($user->wallet_topup*1, 2);?></div>
                                  <div class="stat-change">
                                      <i class="fas fa-coins"></i>
                                      Available Balance
                                  </div>
                              </div>

                              <div class="wallet-card">
                                  <div class="wallet-icon">
                                      <i class="fas fa-piggy-bank"></i>
                                  </div>
                                  <div class="wallet-label">Main Wallet</div>
                                  <div class="wallet-amount"><?php echo SITE_CURRENCY; ?><?php echo number_format($user->wallet*1, 2);?></div>
                                  <div class="stat-change">
                                      <i class="fas fa-university"></i>
                                      Available Balance
                                  </div>
                              </div>
                          </div>

                          <!-- Self Transfer Form -->
                          <div class="transfer-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-exchange-alt"></i> Self Transfer</h3>
                              </div>
                              <div class="card-body">
                                  <form action="fund_transfer3_model.php" method="post" id="selfTransferForm">
                                      <!-- Amount Field -->
                                      <div class="form-group">
                                          <label class="form-label" for="amount">Transfer Amount</label>
                                          <input class="form-control" type="number" id="amount" name="amount" step="0.01" min="1" required placeholder="Enter amount to transfer">
                                          <div class="invalid-feedback">Please enter a valid amount</div>
                                      </div>

                                      <!-- Fund Type Field -->
                                      <div class="form-group">
                                          <label class="form-label" for="type">Transfer Type</label>
                                          <select class="form-control" name="type" id="type" required>
                                              <?php foreach ($fund_type_arr as $key => $value){?>
                                              <option value="<?php echo $key;?>"><?php echo $value;?></option>
                                              <?php }?>
                                          </select>
                                      </div>

                                      <!-- Remark Field -->
                                      <div class="form-group">
                                          <label class="form-label" for="remark">Transfer Note</label>
                                          <textarea class="form-control" id="remark" name="remark" rows="3" required maxlength="250" placeholder="Enter transfer description or note"></textarea>
                                          <div class="invalid-feedback">Please enter a remark</div>
                                      </div>

                                      <!-- Submit Button -->
                                      <button type="submit" class="btn btn-transfer">
                                          <i class="fas fa-sync-alt"></i> Transfer Funds
                                      </button>
                                  </form>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Self Transfer page initialized');
        });
    }

    // Initialize the app
    initializeApp();
})();

// Enhanced form validation for self transfer
document.getElementById('selfTransferForm').onsubmit = function(e) {
    const amount = document.getElementById('amount').value;
    const type = document.getElementById('type').value;
    const remark = document.getElementById('remark').value;

    let isValid = true;

    // Validate amount
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
        document.getElementById('amount').classList.add('is-invalid');
        isValid = false;
    } else {
        document.getElementById('amount').classList.remove('is-invalid');
        document.getElementById('amount').classList.add('is-valid');
    }

    // Validate type
    if (!type) {
        document.getElementById('type').classList.add('is-invalid');
        isValid = false;
    } else {
        document.getElementById('type').classList.remove('is-invalid');
        document.getElementById('type').classList.add('is-valid');
    }

    // Validate remark
    if (!remark || remark.trim() === '') {
        document.getElementById('remark').classList.add('is-invalid');
        isValid = false;
    } else {
        document.getElementById('remark').classList.remove('is-invalid');
        document.getElementById('remark').classList.add('is-valid');
    }

    if (!isValid) {
        e.preventDefault();
        return false;
    }

    // Show confirmation dialog
    const transferType = document.getElementById('type').options[document.getElementById('type').selectedIndex].text;
    const confirmMessage = `Are you sure you want to transfer ${amount} ${transferType}?`;
    if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
    }

    return true;
};

// Real-time validation
document.getElementById('amount').addEventListener('input', function() {
    if (this.value && !isNaN(this.value) && parseFloat(this.value) > 0) {
        this.classList.remove('is-invalid');
    }
});

document.getElementById('type').addEventListener('change', function() {
    if (this.value) {
        this.classList.remove('is-invalid');
    }
});

document.getElementById('remark').addEventListener('input', function() {
    if (this.value.trim() !== '') {
        this.classList.remove('is-invalid');
    }
});

// Add balance checking functionality
document.getElementById('amount').addEventListener('blur', function() {
    const amount = parseFloat(this.value);
    const type = document.getElementById('type').value;

    if (amount && type) {
        // Here you can add AJAX call to check if user has sufficient balance
        // For now, just show a visual indicator
        console.log('Checking balance for transfer type:', type, 'amount:', amount);
    }
});
</script>

</body>
</html>
