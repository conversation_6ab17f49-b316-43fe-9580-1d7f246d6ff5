<?php include_once '../lib/config.php';
if(SITE_WORKING_STATUS){
    echo '<center style="position: relative; top: 100px;"><h1>This site is under maintenance</h1></center>';die;
}
if(isset($_GET['ref']) && !empty($_GET['ref'])){$uid = $_GET['ref'];}
elseif(isset($_SESSION['userid']) && !empty($_SESSION['userid'])){$uid = $_SESSION['userid'];}
else{$uid = '0';}
//redirect('../register.php?r='.$uid);die;
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title_name = isset($title) ? SITE_NAME . ' | ' . $title : SITE_NAME . ' | Member Register';?></title>
<meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="icon" type="image/png" href="assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="assets/cy/css/media.css?vs=100">
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
  width: 160px;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 160px;
}
.field--contact .field-icon::before{background-color:transparent;}
.field--contact  img{margin-top:-10px;height:17px;}
</style>

<link rel="shortcut icon" href="favicon.ico" />
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="index.php" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
      </div>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-auth-panel-block">
        <div class="mobile-auth-panel">
          <div class="mobile-auth-panel-btn-block">
            <a  href='https://tether50.com/soft/member/index.php' class="topline-login-btn">
            <div class="topline-login-btn__text">Sign In</div>
            <div class="topline-login-btn__icon"></div>
            </a>
          </div>
          <div class="mobile-auth-panel-btn-block"> <a href="https://tether50.com/soft/member/register.php" class="topline-registration-btn">
            <div class="topline-registration-btn__text">Register</div>
            <div class="topline-registration-btn__icon"></div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-menu">
        <ul>
          <li class="mobile-menu-item mobile-menu-item--games"> <a href="https://tether50.com/about.php" class="mobile-menu-link mobile-menu-link--games">
            <div class="mobile-menu-link__text"> About Us </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="https://tether50.com/index.php#help" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> How It Works </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/game.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Gaming  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/lotteries.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Lotteries  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/staking.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Staking  </div>
            </a> </li>

         <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="affiliate-program.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Affiliate Program  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="help-center.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Help Center  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--affiliate-program"> <a href="contact.php" class="mobile-menu-link mobile-menu-link--news">
            <div class="mobile-menu-link__text"> Contact Us </div>
            </a> </li>


        </ul>
      </div>
    </div>
  </div>
</div>
<header>
  <div class="topline-block-wrapper">
    <div class="topline-block">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="topline">
              <div class="topline-left">
                <div class="logo-wrapper"> <a href="https://tether50.com/index.php" class="logo">
                  <div class="logo-img"> <img src="assets/logo.png" alt="img"> </div>
                  </a> </div>
                <div class="topmenu-block">
                  <div class="topmenu">
                    <ul>
          <li class="mobile-menu-item mobile-menu-item--games"> <a href="https://tether50.com/about.php" class="mobile-menu-link mobile-menu-link--games">
            <div class="mobile-menu-link__text"> About Us </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--lottery"> <a href="https://tether50.com/index.php#how" class="mobile-menu-link mobile-menu-link--lottery">
            <div class="mobile-menu-link__text"> How It Works </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/game.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Gaming  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/lotteries.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Lotteries  </div>
            </a> </li>

            <li class="mobile-menu-item mobile-menu-item--staking"> <a href="https://tether50.com/staking.php" class="mobile-menu-link mobile-menu-link--staking">
            <div class="mobile-menu-link__icon"> <img class="image" src="assets/cy/images/theme/topmenu-link-icon--city.png" alt=""> </div>
            <div class="mobile-menu-link__text"> Staking  </div>
            </a> </li>

			<li class="mobile-menu-item mobile-menu-item--help-center"> <a href="affiliate-program.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Affiliate Program  </div>
            </a> </li>

		 <li class="mobile-menu-item mobile-menu-item--help-center"> <a href="help-center.php" class="mobile-menu-link mobile-menu-link--affiliate-program">
            <div class="mobile-menu-link__text"> Help Center  </div>
            </a> </li>
          <li class="mobile-menu-item mobile-menu-item--affiliate-program"> <a href="contact.php" class="mobile-menu-link mobile-menu-link--news">
            <div class="mobile-menu-link__text"> Contact Us </div>
            </a> </li>



        </ul>
                  </div>
                </div>
              </div>
              <div class="topline-right">
                <div class="topline-panel-block">
                  <div class="topline-panel">
                    <div class="topline-lang-panel-block">
                      <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
                        <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                        <div class="current-lang__text">EN</div>
                        </a>
                        <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="?lang=default" class="lang-link">
                          <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                          <div class="lang-link__text">EN</div>
                          </a> </div>
                      </div>
                    </div>
                    <div class="topline-login-btn-block">
                      <a href='https://tether50.com/soft/member/index.php' class="topline-login-btn">
                      <div class="topline-login-btn__text">Sign In</div>
                      <div class="topline-login-btn__icon"></div>
                      </a>
                    </div>
                    <div class="topline-registration-btn-block"> <a href="https://tether50.com/soft/member/register.php" class="topline-registration-btn">
                      <div class="topline-registration-btn__text">Register</div>
                      <div class="topline-registration-btn__icon"></div>
                      </a> </div>
                    <div class="mobile-panel-btn-block">
                      <button type="button" class="mobile-panel-btn" onclick="show()"></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<section class="section-registration">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="registration-block">
          <div class="registration">
            <div class="registration-left">
              <div class="registration-feature-items-block">
                <div class="registration-feature-items">
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Exciting and fair games and lotteries </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Staking offers with returns of up to 200% </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> We ensure your security and anonymity </div>
                    </div>
                  </div>
                  <div class="registration-feature-item-wrapper">
                    <div class="registration-feature-item">
                      <div class="registration-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                      <div class="registration-feature-item__descr"> Join our generous affiliate program </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="registration-left-bg-image"> <img class="image" src="assets/cy/images/theme/registration-left-image.png" alt=""> </div>
            </div>
            <div class="registration-right">
              <div class="registration-form-block">
                <h2> Create an account </h2>

                <?php if(isset($_SESSION['SetMessage'])): ?>
                <div class="alert alert-<?php echo strpos($_SESSION['SetMessage'], 'danger') !== false ? 'danger' : 'success'; ?>" style="margin-bottom: 20px;">
                    <?php echo getMessage(); ?>
                </div>
                <?php endif; ?>

                <form action="register_model.php" id="loginForm" method="post" class="registration-form form">
                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Referral Id  <span class="field-required-star">*</span></div>
                    </div>
                    <div class="field field--input field--have-icon field--pin">
                      <div class="field-icon"></div>
                      <input type="text" name="refer_id" id="refer_id" maxlength="20" class="form-control" value="<?php echo get_user_details($uid)->login_id;?>" required="required" onBlur="check_sponser(this.value);">
                    </div>
                    <span id="sponser" class="error-message"></span>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Select Position  <span class="field-required-star">*</span></div>
                    </div>
                    <div class="field field--select">
                     <select name="position" id="position" class="select" required="required" data-style="btn-outline-secondary">
                        <option value="" disabled="disabled" selected="selected">Select position</option>
                        <option value="L" <?php if((isset($_SESSION['position']) && $_SESSION['position']=='L') || (isset($_GET['p']) && $_GET['p']=='L')){echo "selected='selected'";}elseif(isset($_SESSION['position']) && $_SESSION['position']=='R'){echo "disabled='disabled'";}?>>Left</option>
                        <option value="R" <?php if((isset($_SESSION['position']) && $_SESSION['position']=='R') || (isset($_GET['p']) && $_GET['p']=='R')){echo "selected='selected'";}elseif(isset($_SESSION['position']) && $_SESSION['position']=='L'){echo "disabled='disabled'";}?>>Right</option>
                     </select>
                    </div>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Name  <span class="field-required-star">*</span></div>
                    </div>
                    <div class="field field--input field--have-icon field--username">
                      <div class="field-icon"></div>
                      <input type="text" name="name" id="name" maxlength="50" class="form-control" required="required" pattern="[a-zA-Z ]+" data-inputmask-placeholder="">
                    </div>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Email <span class="field-required-star">*</span> </div>
                    </div>
                    <div class="field field--input field--have-icon field--email">
                      <div class="field-icon"></div>
                      <input id="email" type="email" name="email" maxlength="100" autocomplete="off" required="required" onBlur="check_email(this.value);">
                    </div>
                    <span id="email_error" class="error-message"></span>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Contact <span class="field-required-star">*</span> </div>
                    </div>
                    <div class="field field--input field--have-icon field--contact">
                      <div class="field-icon"><img src="assets/phone.png"></div>
                      <input id="mobile" type="text" name="mobile" maxlength="10" autocomplete="off" required="required" onBlur="check_mobile(this.value);" pattern="[0-9]{10,10}">
                    </div>
                    <span id="mobile_error" class="error-message"></span>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Password <span class="field-required-star">*</span> </div>
                    </div>
                    <div class="field field--input field--have-icon field--password">
                      <div class="field-icon"></div>
                      <input type="password" name="password" id="password" maxlength="20" class="form-control" required="required" onchange="form.confirm_password.pattern = this.value;">
                      <div class="field-right-panel-block">
                        <div class="field-right-panel">
                          <div class="change-pswd-type-link-block">
                            <button type="button" class="change-pswd-type-link"></button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="field-block">
                    <div class="field-title-block">
                      <div class="field-title"> Confirm Password <span class="field-required-star">*</span> </div>
                    </div>
                    <div class="field field--input field--have-icon field--password">
                      <div class="field-icon"></div>
                      <input type="password" name="confirm_password" id="confirm_password" maxlength="20" class="form-control" required="required" pattern="">
                    </div>
                    <span id="password_error" class="error-message"></span>
                  </div>

                  <div class="field-block">
                    <div class="field field--checkbox">
                      <label class="custom-checkbox-label">
                        <input type="checkbox" name="checkbox" value="1" class="checkbox" id="flexCheckDefault" required="required">
                        <span class="custom-checkbox"></span> <span class="custom-checkbox-label-text">I have read and accept the terms of the <a href="../../terms.pdf" target="_blank">User Agreement</a> and <a href="#">Privacy Policy</a>.</span> </label>
                    </div>
                  </div>

                  <div class="form-button-block">
                    <button type="submit" class="green-gr-btn send-btn" id="submit">
                    <div class="send-btn__text">Register Now</div>
                    <div class="send-btn__icon"></div>
                    </button>
                  </div>

                  <div class="form-bottom-note-block">
                    <div class="form-bottom-note"> Already have an account?
                      <a href="https://tether50.com/soft/member/index.php">Sign In </a>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="registration-mobile-feature-items-block">
          <div class="registration-mobile-feature-items">
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--dice-five.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Exciting and fair games and lotteries </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--checkerboard.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Staking offers with returns of up to 200% </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--shield-check.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> We ensure your security and anonymity </div>
              </div>
            </div>
            <div class="registration-mobile-feature-item-wrapper">
              <div class="registration-mobile-feature-item">
                <div class="registration-mobile-feature-item__icon"> <img class="image" src="assets/cy/images/svg/registration/registration-feature-item-icon--rocket-launch.svg" alt=""> </div>
                <div class="registration-mobile-feature-item__descr"> Join our generous affiliate program </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<footer>
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="footer-content">
        <div class="row">
          <div class="col-12 col-md-5 col-lg-3 col-xxl-2">
            <div class="footer-content-left">
              <div class="logo-wrapper"> <a href="/" class="logo">
                <div class="logo-img"> <img src="assets/logo.png" alt=""> </div>
                </a> </div>
              <div class="footer-copy"> © 2025 TETHER50 <br>
                All rights reserved </div>
            </div>
          </div>
          <div class="col-12 col-md-7 col-lg-9 col-xxl-10">
            <div class="footer-content-right">
              <div class="footer-menu-blocks">
                <div class="footer-menu-block footer-menu-block--product">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Products </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="game.php" class="footer-menu-link">Games</a> </li>
                        <li class="footer-menu-item"> <a href="game.php" class="footer-menu-link">Gaming API</a> </li>
                        <li class="footer-menu-item"> <a href="lotteries.php" class="footer-menu-link">Lotteries</a> </li>
                        <li class="footer-menu-item"> <a href="staking.php" class="footer-menu-link">Staking </a> </li>
                        <li class="footer-menu-item"> <a href="affiliate-program.php" class="footer-menu-link">Affiliate Program</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--support">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Platform </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="https://tether50.com/index.php" class="footer-menu-link">Home</a> </li>
                        <li class="footer-menu-item"> <a href="https://tether50.com/about.php" class="footer-menu-link">About Us</a> </li>
                        <li class="footer-menu-item"> <a href="https://tether50.com/index.php#how" class="footer-menu-link">How It Works</a> </li>
                        <li class="footer-menu-item"> <a href="help-center.php" class="footer-menu-link">Help Center</a> </li>
                        <li class="footer-menu-item"> <a href="contact.php" class="footer-menu-link">Contact Us</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--terms">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Terms & Conditions </div>
                    <div class="footer-menu">
                      <ul>
                        <li class="footer-menu-item"> <a href="terms-of-service.php" class="footer-menu-link">Terms of Service</a> </li>
                        <li class="footer-menu-item"> <a href="risk-disclosure.php" class="footer-menu-link">Risk Disclosure</a> </li>
                        <li class="footer-menu-item"> <a href="responsible-gaming.php" class="footer-menu-link">Responsible Gaming</a> </li>
                        <li class="footer-menu-item"> <a href="privacy-policy.php" class="footer-menu-link">Privacy Policy</a> </li>
                        <li class="footer-menu-item"> <a href="cookie-policy.php" class="footer-menu-link">Cookie Policy</a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="footer-menu-block footer-menu-block--contact">
                  <div class="footer-menu-block-inner">
                    <div class="footer-menu-block-title"> Contact details </div>
                    <div class="footer-contact-items-block">
                      <div class="footer-contact-items">
                        <div class="footer-contact-item"> <a href="" class="footer-contact-email-link">
                          <div class="footer-contact-email-link__icon"></div>
                          <div class="footer-contact-email-link__text"> <EMAIL> </div>
                          </a> </div>
                        <div class="footer-contact-item">
                          <div class="footer-contact-address">
                            <div class="footer-contact-address__icon"></div>
                            <div class="footer-contact-address__text"> TETHER LIMITED<br />
                              Company number:<br />
                              13957078 <br />
                              <a href="https://find-and-update.company-information.service.gov.uk/company/13957078">Verify </a></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="footer-copy">  2025 TETHER50 . <br>
                All rights reserved </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </footer>

</div>

        <!-- jQuery -->
        <script src="assets/cy/libs/jquery/jquery-3.6.0.min.js"></script>
        <script>
            // Initialize submit button as disabled
            $(document).ready(function(){
                $("#submit").attr("disabled", "true");

                // Initialize select picker for position dropdown
                $('#position').selectpicker({
                    style: 'btn-outline-secondary',
                    size: 4
                });

                // Check if referral ID is pre-filled and validate it automatically
                var referralId = $("#refer_id").val();
                if(referralId && referralId.trim() !== '') {
                    check_sponser(referralId);
                }

                // Password confirmation validation
                $("#confirm_password").on("input", function() {
                    if($(this).val() !== $("#password").val()) {
                        $("#password_error").html("Passwords do not match").addClass("error-message").removeClass("success-message");
                        $("#submit").attr("disabled", "true");
                    } else {
                        $("#password_error").html("").removeClass("error-message");
                        if($("#sponser").hasClass("success-message") &&
                           $("#email_error").hasClass("success-message") &&
                           $("#mobile_error").hasClass("success-message")) {
                            $("#submit").removeAttr("disabled");
                        }
                    }
                });

                // Override AJAX form submission to use traditional form submission
                // Wait for all scripts to load, then override the form submission
                setTimeout(function() {
                    // Remove any existing AJAX event handlers
                    $("#loginForm").off('submit');

                    // Add our custom form submission handler for traditional submission
                    $("#loginForm").on('submit', function(e) {
                        // Check if all validations pass before allowing submission
                        if($("#submit").is(":disabled")) {
                            e.preventDefault();
                            return false;
                        }
                        // Allow normal form submission to register_model.php
                        return true;
                    });
                }, 1000);
            });

            function check_sponser(refer_id){
                if(!refer_id) {
                    $("#sponser").html("Please enter sponsor ID").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                // Show loading indicator
                $("#sponser").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'sponsor','refer_id':refer_id},function(data){
                    if(data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#sponser").html('<i class="fa fa-times-circle"></i> Invalid sponsor ID').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#sponser").html('<i class="fa fa-check-circle"></i> ' + data.name + " - Valid sponsor ID").addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function check_mobile(mobile){
                if(!mobile) {
                    $("#mobile_error").html("Please enter mobile number").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                pattern = /^[0-9]+$/;

                // Show loading indicator
                $("#mobile_error").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'mobile','mobile':mobile},function(data){
                    console.log(data);
                    if(!data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-times-circle"></i> Mobile number already exists').addClass("error-message").removeClass("success-message");
                    }
                    else if(mobile.length != 10){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-info-circle"></i> Mobile must be 10 digits').addClass("error-message").removeClass("success-message");
                    }
                    else if(!pattern.test(mobile)){
                        $("#submit").attr("disabled", "true");
                        $("#mobile_error").html('<i class="fa fa-info-circle"></i> Mobile must contain only numbers').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#mobile_error").html('<i class="fa fa-check-circle"></i> Valid mobile number').addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function check_email(email){
                if(!email) {
                    $("#email_error").html("Please enter email address").addClass("error-message").removeClass("success-message");
                    $("#submit").attr("disabled", "true");
                    return;
                }

                pattern = /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_.-])+\.([a-zA-Z])+([a-zA-Z])+/;

                // Show loading indicator
                $("#email_error").html('<i class="fa fa-spinner fa-spin"></i> Verifying...').removeClass("error-message success-message");

                $.get("../lib/get_availability.php",{'action':'email','email':email},function(data){
                    if(!data.invalid){
                        $("#submit").attr("disabled", "true");
                        $("#email_error").html('<i class="fa fa-times-circle"></i> Email already exists').addClass("error-message").removeClass("success-message");
                    }
                    else if(!pattern.test(email)){
                        $("#submit").attr("disabled", "true");
                        $("#email_error").html('<i class="fa fa-info-circle"></i> Invalid email format').addClass("error-message").removeClass("success-message");
                    }
                    else{
                        $("#email_error").html('<i class="fa fa-check-circle"></i> Valid email address').addClass("success-message").removeClass("error-message");
                        checkAllValidations();
                    }
                },"json");
            }

            function checkAllValidations() {
                if($("#sponser").hasClass("success-message") &&
                   $("#email_error").hasClass("success-message") &&
                   $("#mobile_error").hasClass("success-message") &&
                   ($("#password").val() === $("#confirm_password").val()) &&
                   $("#confirm_password").val() !== "") {
                    $("#submit").removeAttr("disabled");
                }
            }
        </script>

        <!-- Include signup.php JavaScript libraries in correct order -->
        <script defer src="assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/autosize/autosize.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/swiper/swiper-bundle.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/bootstrap-select-1.14.0/dist/js/i18n/defaults-en_US.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/clipboard.js-master/dist/clipboard.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/plyr/dist/plyr.polyfilled.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/chart.js-3.7.1/dist/chart.min.js?vs=100"></script>
        <script defer src="assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
        <script src="assets/cy/js/common.js?vs=100" defer></script>
        <script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.17/jquery.inputmask.min.js"></script>
        <script defer src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>

        <!-- Override AJAX behavior for traditional form submission -->
        <script>
        // Additional override to ensure form submits traditionally
        $(document).ready(function() {
            // Wait for all deferred scripts to load
            setTimeout(function() {
                // Remove any AJAX form handlers that might be added by the framework
                $("form[action='register_model.php']").off('submit');

                // Ensure our form submits normally
                $("#loginForm").on('submit', function(e) {
                    if($("#submit").is(":disabled")) {
                        e.preventDefault();
                        return false;
                    }
                    // Allow traditional form submission
                    return true;
                });
            }, 1500);
        });
        </script>

        <script>
            function show(){
               document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
            }
            function closeSidebar(){
                document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
            }
        </script>

    </body>
</html>
