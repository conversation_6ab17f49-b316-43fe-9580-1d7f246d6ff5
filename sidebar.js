/**
 * Sidebar Dropdown Functionality
 * Works for both mobile and desktop sidebars
 */

// Function to handle dropdown toggle
function openDropdown(element) {
    var dropdownContainer = element.nextElementSibling;
    var isActive = element.classList.contains('dactive');
    
    // Close all dropdowns first
    var allDropdowns = document.querySelectorAll('.dropdown-btn');
    var allContainers = document.querySelectorAll('.dropdown-container');
    
    allDropdowns.forEach(function(dropdown) {
        dropdown.classList.remove('dactive');
    });
    
    allContainers.forEach(function(container) {
        container.style.display = 'none';
    });
    
    // If this dropdown wasn't active, open it
    if (!isActive) {
        element.classList.add('dactive');
        dropdownContainer.style.display = 'block';
    }
}

// Initialize sidebar functionality when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar functionality initialized');
    
    // Add click handlers for dropdown buttons
    var dropdownButtons = document.querySelectorAll('.dropdown-btn');
    dropdownButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Dropdown button clicked');
            openDropdown(this);
        });
    });
    
    console.log('Found ' + dropdownButtons.length + ' dropdown buttons');
});

// jQuery version for compatibility
$(document).ready(function() {
    console.log('jQuery sidebar functionality initialized');
    
    // Dropdown functionality
    $('.dropdown-btn').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('jQuery dropdown clicked');
        openDropdown(this);
    });
    
    // Sidebar toggle functionality (from common.js)
    $("body").on("click", ".db-side-toggle-panel-btn", function (e) {
        e.preventDefault();
        if ($(this).is(".active")) {
            $(this).removeClass("active")
            $(".db-page").removeClass("db-page--wide-menu")
        }
        else {
            $(this).addClass("active")
            $(".db-page").addClass("db-page--wide-menu")
        }
    });
    
    // Mobile panel functionality (from common.js)
    $("body").on("click", ".mobile-panel-btn", function (e) {
        e.preventDefault();
    });
    
    $("body").on("click", function (e) {
        var mobilePanel = $(".mobile-panel-block");
        var mobilePanelLink = $(".mobile-panel-btn");
        if (mobilePanelLink.is(e.target)) {
            if ($(".mobile-panel-btn").is(".active")) {
                mobilePanelLink.removeClass("active")
                mobilePanel.removeClass("active")
            }
            else {
                mobilePanelLink.addClass("active")
                mobilePanel.addClass("active")
            }
        } else {
            if (!mobilePanel.is(e.target) && mobilePanel.has(e.target).length === 0) {
                if ($(".mobile-panel-btn").is(".active")) {
                    $(".mobile-panel-btn").removeClass("active")
                    mobilePanel.removeClass("active")
                } else {
                }
            }
        }
    });
    
    $("body").on("click", ".mobile-panel-close-btn", function (e) {
        e.preventDefault();
        $(".mobile-panel-btn").removeClass("active")
        $(".mobile-panel-block").removeClass("active")
    });
});
