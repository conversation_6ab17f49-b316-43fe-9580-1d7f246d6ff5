<?php
ob_start();
session_start();
include_once '../lib/config.php';
$title = 'Dashboard';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];

$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$sponsor = get_user_details($user->refer_id);
$reward_arr = get_reward();
$_address = strtolower(SITE_CURRENCY_) . '_address';
function get_child_bv_total3($uid, $p = 'L'){
    $amt = @my_fetch_object(my_query("SELECT (teamb + topup) as amount FROM user WHERE placement_id = '".$uid."' AND position = '".$p."'"))->amount;
    $amt = ($amt > 0) ? $amt : 0;
    return $amt;
}
$total_in = get_sum('income_binary', 'amount', "uid='" . $uid . "'") + get_sum('income_royalty', 'amount', "uid='" . $uid . "'") + get_sum('income_growth', 'amount', "uid='" . $uid . "'") + get_sum('income_level', 'amount', "uid='" . $uid . "'") + get_sum('income_direct', 'amount', "uid='" . $uid . "'");

$max = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 0,1"))->amount;
$max = ($max) ? $max : 0;
// $max2 = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 1,1"))->amount;
// $max2 = ($max2) ? $max2 : 0;
$max2 = 0;
$max3 = $user->teamb - $max - $max2;
$max3 = ($max3 > 0) ? $max3 : 0;

// Get active orders count
$active_orders_query = "SELECT COUNT(*) as active_count
                      FROM investments
                      WHERE status = 0
                      AND uid = '$uid'";
$active_result = my_query($active_orders_query);
$active_orders = mysqli_fetch_object($active_result)->active_count;

// Check if user has trade_active status and active orders
$trade_active = $user->trade_status;

// Get team counts and business values
$left_team_total = get_count('user', "placement_id='$uid' AND position='L'");
$left_team_active = get_count('user', "placement_id='$uid' AND position='L' AND status=0");
$left_team_business = get_child_bv_total3($uid, 'L');

$right_team_total = get_count('user', "placement_id='$uid' AND position='R'");
$right_team_active = get_count('user', "placement_id='$uid' AND position='R' AND status=0");
$right_team_business = get_child_bv_total3($uid, 'R');

// Get direct referrals
$left_direct_total = get_count('user', "refer_id='$uid' AND position='L'");
$left_direct_active = get_count('user', "refer_id='$uid' AND position='L' AND package > 0");
$left_direct_business = @my_fetch_object(my_query("SELECT SUM(teamb + topup) as total FROM user WHERE refer_id='$uid' AND position='L'"))->total;
$left_direct_business = ($left_direct_business > 0) ? $left_direct_business : 0;

$right_direct_total = get_count('user', "refer_id='$uid' AND position='R'");
$right_direct_active = get_count('user', "refer_id='$uid' AND position='R' AND package > 0");
$right_direct_business = @my_fetch_object(my_query("SELECT SUM(teamb + topup) as total FROM user WHERE refer_id='$uid' AND position='R'"))->total;
$right_direct_business = ($right_direct_business > 0) ? $right_direct_business : 0;

// Get wallet balances
$activation_wallet = isset($user->wallet_topup) ? $user->wallet_topup : 0;
$cashback_wallet = isset($user->balance2) ? $user->balance2 : 0;
$earning_wallet = isset($user->wallet) ? $user->wallet : 0;
$gaming_wallet = isset($user->balance4) ? $user->balance4 : 0;

// Get earning details
$non_working_bonus = get_sum('income_direct', 'amount', "uid='$uid'");
$matching_bonus = get_sum('income_binary', 'amount', "uid='$uid'");
$reward_bonus = get_sum('income_royalty', 'amount', "uid='$uid'");
$lottery_bonus = 0; // Table doesn't exist, set to 0
$game_bonus = 0; // Table doesn't exist, set to 0
$daily_earning = get_sum('income_growth', 'amount', "uid='$uid'");
$daily_withdrawal = get_sum('withdrawal_block', 'amount', "uid='$uid'") || 0; // Table doesn't exist, set to 0
$total_earning = $total_in;
$total_withdrawal = 0; // Table doesn't exist, set to 0
$carry_forward_business = isset($user->teamb) ? $user->teamb : 0;
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Dashboard - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.1/css/all.min.css" integrity="sha512-9my9Mb2+0YO+I4PUCSwUYO7sEK21Y0STBAiFEYoWtd2VzLEZZ4QARDrZ30hdM1GlioHJ8o8cWQiy8IAb1hy/Hg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard active">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
    <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>


             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">
              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img src="assets/logo.png" class="image" alt=""> </div>
                </a> </div>
            </div>
            <div class="db-page-topline-panel__right__content">
              <div class="topline-lang-panel-block">
                <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="current-lang__text">EN</div>
                  </a>
                  <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                    <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                    <div class="lang-link__text">EN</div>
                    </a> </div>
                </div>
              </div>
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
              <div class="mobile-panel-btn-block">
                <button type="button" class="mobile-panel-btn" onclick="show()"></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
    function show(){
       document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }
    function closeSidebar(){
        document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
</script>
<script>
/* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content - This allows the user to have multiple dropdowns without any conflict */
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
  dropdown[
i].addEventListener("click", function() {
    this.classList.toggle("active");
	this.classList.toggle("dactive");
    var dropdownContent = this.nextElementSibling;
    if (dropdownContent.style.display === "block") {
      dropdownContent.style.display = "none";


    } else {
      dropdownContent.style.display = "block";
    }
  });
}
</script>
<style>
.acct_icon img {
  width: 20px;
  margin-top: 5px;
}
.dashboard-info-stat-field__value {
  font-size: 16px;
}
.dashboard-info-stat-field {
  border-radius: 8px;
  padding: 8px 8px 8px 16px;
  background: #0e1b28;
  display: flex;
  flex-direction: inherit;
}
.dashboard-info-stat-field__content {
  width: 100%;
  margin-left: 20px;
}
.dashboard-info-stat-field__title {
  color: #fff;
}
.dashboard-info-affiliate-copy-link-btn.purple-btn.iconed-btn.copy-btn {
  height: 70px;
}
.dashboard-info-panel .topline-refill-btn {
  width: 213px;
  height: 120px;
}
.dashboard-info-descr .amt {
  font-weight: bold;
}

.wallet  .dashboard-info-stat-field.dashboard-info-stat-field--wallet {
  background: none;
}
.wallet .acct_icon img{width: 40px;
  padding: 0;
  margin-top: 0;
  height: 40px;}
.wallet .acct_icon {
  border: 1px solid #fff;
  padding: 20px;
  border-radius: 50%;
}
.wallet .dashboard-info-stat-field__value {
  font-size: 14px;
}
.wallet .dashboard-info-stat-field__title {
  color: #fff;
  font-size: 20px;
  margin-top:10px;
}
.w4{filter:brightness(0) invert(1);}

.dashboard-info-panel {
    display: flex !important;
    align-items: flex-end;
    flex-wrap: wrap !important;
  }
  .topline-refill-btn-block {
  margin-bottom: 10px;
}
.team .dashboard-info-stat-field {
width: 213px;
  height: 150px;
}
.team .dashboard-info-stat-field__title {
  margin-top: 30px;
}
.team .acct_icon img {
  width: 20px;
  margin: auto;}
 .team .acct_icon{display:flex;}
  .team-refill-btn{text-decoration: none !important;}
 .earn .acct_icon {padding: 10px !important; }
.earn  .topline-refill-btn{
  width: 168.8px !important;
  height: 70px !important;
  padding:5px;
}
.earn .acct_icon img {
  width: 20px;
  padding: 0;
  margin-top: 0;
  height: 20px;
}
.earn .dashboard-info-stat-field {
  padding: 0;
}
.earn .dashboard-info-stat-field__content {
  width: 100%;
  margin-left: 10px;
}
.earn .wallet .dashboard-info-stat-field__title {
  margin-top: 5px;
}
.earn .wallet .dashboard-info-stat-field__value {
  font-size: 9px;
}
.earn .topline-refill-btn{background:#0e1b28 !important;}
.amt{color: #17c964;
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);    background-clip: border-box;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;}

.mt-10{margin-top:10px;}
.earn .acct_icon {
  border: 2px solid transparent;
  background: linear-gradient(#0e1b28, #0e1b28) padding-box, linear-gradient(to right,#17c964, #0170ef) border-box;
  border-radius: 50%;
}
.team .dashboard-info-stat-field {
  background: transparent;}
  .team .topline-refill-btn{text-align:left !important;}
  .team .dashboard-info-stat-field{padding:8px 8px 8px 0px; }
  .h {
  font-size: 18px;
}
.earn .topline-refill-btn{display:block !important;}
.earn .dashboard-info-stat-field.dashboard-info-stat-field--wallet {
  padding-top: 10px;
}
@media only screen and (max-width: 460px) {
	.earn .topline-refill-btn-block.flex-row-item {
  width: 50%;
}
.earn .dashboard-info-stat-field.dashboard-info-stat-field--wallet {
  padding-top: 4px;
}

  .dashboard-info-panel .topline-refill-btn {
  width: 160px;
  height: 100px;
}
.wallet .dashboard-info-stat-field__title {
  font-size: 15px;
}
.wallet .acct_icon {
  border: 1px solid #fff;
  padding: 15px;
  border-radius: 50%;
}
.wallet .acct_icon img {
  width: 30px !important;
  height: 30px !important;
}
.wallet-cont .flex-row-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.wallet-cont .flex-row-item {
    display: block;
    width: 50%;
    padding: 5px;
  }
 .wallet-cont  .topline-refill-btn {
  display: block;
}

.team .topline-refill-btn{padding:0px;}
.h {
  font-size: 14px;
}.topline-refill-btn.wallet {
  width: 100% !important;
}
.wallet .dashboard-info-stat-field {
  padding: 8px 8px 8px 0px;}
  .team .topline-refill-btn-block {
  display: block;
  width: 50%;
  padding:5px;
}
.tm {
    width: 100% !important;
  }
}
 </style>
              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="dashboard-block">
                    <div class="dashboard">
                      <div class="dashboard-left">
                        <div class="dashboard-info-block">
                          <div class="dashboard-info">
                            <h1> Dashboard </h1>
                             <div class="dashboard-info-descr">Active Package : <span class="amt">$<?php echo (isset($user->topup) && $user->topup > 0) ? number_format($user->topup, 2) : '0.00'; ?></span> </div>
                            <div class="dashboard-info-descr"> Account Status : <span class="<?php echo (isset($user->status) && $user->status == 0) ? 'text-success' : 'red-text text-danger'; ?>"><?php echo (isset($user->status) && $user->status == 0) ? 'Active' : 'Inactive'; ?></span> </div>

                            <div class="dashboard-info-stats-block dashboard-info-stat-field--total">
                              <div class="dashboard-info-stats-title"> Personal Details </div>
                              <div class="dashboard-info-stat-fields-block">
                                <div class="dashboard-info-stat-fields">
                                  <div class="dashboard-info-stat-field-wrapper dashboard-info-stat-field-wrapper--total ">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--total">
                                      <div class="acct_icon"><img src="assets/username.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> User Name </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo isset($user->name) ? $user->name : 'N/A'; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="dashboard-info-stat-field-wrapper  dashboard-info-stat-field-wrapper--total">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--total">
                                      <div class="acct_icon"><img src="assets/userid.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> Your Userid </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo $uid; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="dashboard-info-stat-field-wrapper  dashboard-info-stat-field-wrapper--total">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--total">
                                      <div class="acct_icon"><img src="assets/date.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> Registration Date </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo isset($user->datetime) ? date('d/m/Y', strtotime($user->datetime)) : 'N/A'; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="dashboard-info-stat-field-wrapper dashboard-info-stat-field-wrapper--total">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--total">
                                      <div class="acct_icon"><img src="assets/date.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> Activation Date </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo (isset($user->topup) && $user->topup > 0) ? (isset($user->datetime) ? date('d/m/Y', strtotime($user->datetime)) : 'N/A') : 'Not Activated'; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="dashboard-info-stat-field-wrapper  dashboard-info-stat-field-wrapper--total">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--total">
                                      <div class="acct_icon"><img src="assets/userid.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> Sponsor Id </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo isset($user->refer_id) ? $user->refer_id : 'N/A'; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="dashboard-info-stat-field-wrapper  dashboard-info-stat-field-wrapper--total">
                                    <div class="dashboard-info-stat-field dashboard-info-stat-field--casino">
                                      <div class="acct_icon"><img src="assets/username.png"></div>
                                      <div class="dashboard-info-stat-field__content">
                                        <div class="dashboard-info-stat-field__title"> Sponsor Name </div>
                                        <div class="dashboard-info-stat-field__value"> <?php echo ($sponsor) ? $sponsor->name : 'N/A'; ?> </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="dashboard-info-affiliate-block">
                              <div class="dashboard-info-affiliate-title"> Left Referral Link </div>
                              <div class="dashboard-info-panel">
                                <div class="dashboard-info-panel__top">
                                  <div class="dashboard-info-affiliate-link-info-block">
                                    <div class="dashboard-info-affiliate-link-info">
                                      <div class="dashboard-info-affiliate-link-info-share-btn-block">
                                        <button class="dashboard-info-affiliate-link-info-share-btn"></button>
                                      </div>
                                      <div class="dashboard-info-affiliate-link-info-content">
                                        <div class="dashboard-info-affiliate-link-info-link-block">
                                          <div class="dashboard-info-affiliate-link-info-link"> <?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=L </div>
                                        </div>
                                        <div class="dashboard-info-affiliate-link-info-descr"> Share Your Left Referral Link, Invite Your Friends & Get Instant Bonus! </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="dashboard-info-panel__bottom">
                                  <div class="dashboard-info-affiliate-copy-link-btn-block">
                                    <button  onclick="copyReferralLink('<?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=L')"  class="dashboard-info-affiliate-copy-link-btn purple-btn iconed-btn copy-btn" data-clipboard-text="<?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=L" aria-label="Successfully">
                                    <div class="iconed-btn__text">Copy </div>
                                    <div class="iconed-btn__icon"></div>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="dashboard-info-affiliate-block">
                              <div class="dashboard-info-affiliate-title"> Right Referral Link </div>
                              <div class="dashboard-info-panel">
                                <div class="dashboard-info-panel__top">
                                  <div class="dashboard-info-affiliate-link-info-block">
                                    <div class="dashboard-info-affiliate-link-info">
                                      <div class="dashboard-info-affiliate-link-info-share-btn-block">
                                        <button class="dashboard-info-affiliate-link-info-share-btn"></button>
                                      </div>
                                      <div class="dashboard-info-affiliate-link-info-content">
                                        <div class="dashboard-info-affiliate-link-info-link-block">
                                          <div class="dashboard-info-affiliate-link-info-link"> <?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=R </div>
                                        </div>
                                        <div class="dashboard-info-affiliate-link-info-descr"> Share Your Right Referral Link, Invite Your Friends & Get Instant Bonus! </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="dashboard-info-panel__bottom">
                                  <div class="dashboard-info-affiliate-copy-link-btn-block">
                                    <button onclick="copyReferralLink('<?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=R')" class="dashboard-info-affiliate-copy-link-btn purple-btn iconed-btn copy-btn" data-clipboard-text="<?php echo SITE_URL; ?>/soft/member/register.php?ref=<?php echo $uid; ?>&position=R" aria-label="Successfully">
                                    <div class="iconed-btn__text">Copy </div>
                                    <div class="iconed-btn__icon"></div>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="dashboard-info-affiliate-block  wallet-cont">
                              <div class="dashboard-info-affiliate-title"> Wallet Details</div>
                              <div class="dashboard-info-panel flex-row-container mt-10">
                                <div class="topline-refill-btn-block flex-row-item"> <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/w1.png"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> $<?php echo number_format($activation_wallet, 2); ?> </div>
                                      <div class="dashboard-info-stat-field__value"> Activation Wallet</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item"> <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/w2.png"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> $<?php echo number_format($reward_bonus, 2); ?></div>
                                      <div class="dashboard-info-stat-field__value"> Cashback Wallet</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item"> <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/w3.png"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> $<?php echo number_format($earning_wallet, 2); ?> </div>
                                      <div class="dashboard-info-stat-field__value"> Earning Wallet</div>
                                    </div>
                                  </div>
                                  </a></div>
                                <div class="topline-refill-btn-block flex-row-item"><a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img class="w4" src="assets/w4.png"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title">$<?php echo number_format($gaming_wallet, 2); ?> </div>
                                      <div class="dashboard-info-stat-field__value"> Gaming Wallet</div>
                                    </div>
                                  </div>
                                  </a></div>
                              </div>
                            </div>

                            <div class="dashboard-info-affiliate-block ">
                              <div class="dashboard-info-affiliate-title"> Earning Details</div>
                              <div class="dashboard-info-panel earn flex-row-container mt-10">
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($daily_earning, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Non-Working Bonus</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($matching_bonus, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Matching Bonus</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($reward_bonus, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Reward Bonus </div>
                                    </div>
                                  </div>
                                  </a> </div>


                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($lottery_bonus, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Lottery Bonus</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($game_bonus, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Game Bonus</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($daily_earning, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Daily Earning</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($daily_withdrawal, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Daily Withdrawal</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($total_earning, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Total Earning</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($total_withdrawal, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Total Withdrawal</div>
                                    </div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block flex-row-item">
                                <a  class="topline-refill-btn wallet">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="acct_icon"><img src="assets/cy/images/svg/tip-jar-fill.svg"></div>
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="amt">$<?php echo number_format($carry_forward_business, 2); ?></span> </div>
                                      <div class="dashboard-info-stat-field__value"> Carry Forward Business</div>
                                    </div>
                                  </div>
                                  </a> </div>

                              </div>
                            </div>

                            <div class="dashboard-info-affiliate-block team">
                              <div class="dashboard-info-affiliate-title "> Team Details</div>
                              <div class="dashboard-info-panel mt-10">
                                <div class="topline-refill-btn-block"> <a  class="topline-refill-btn tm">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">

                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title">
<p> <span class="h">Left Referral </span><br>

Total : <?php echo $left_direct_total; ?><br>
Active : <?php echo $left_direct_active; ?><br>
Business : <span class="amts">$<?php echo number_format($left_direct_business, 2); ?></span><br></p>
                                       </div>

                                    </div>
                                      <div class="acct_icon"><img src="assets/w1.png"></div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block"> <a  class="topline-refill-btn tm">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="h"> Right Referral </span><br>
Total : <?php echo $right_direct_total; ?><br>
Active : <?php echo $right_direct_active; ?><br>
Business : <span class="amts">$<?php echo number_format($right_direct_business, 2); ?></span><br> </div>
                                    </div>
                                      <div class="acct_icon"><img src="assets/w1.png"></div>
                                  </div>
                                  </a> </div>
                                <div class="topline-refill-btn-block"> <a  class="topline-refill-btn tm">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="h"> Left Team </span><br>
Total : <?php echo $left_team_total; ?><br>
Active : <?php echo $left_team_active; ?><br>
Business : <span class="amts">$<?php echo number_format($left_team_business, 2); ?></span><br> </div>
                                    </div>
                                      <div class="acct_icon"><img src="assets/w1.png"></div>
                                  </div>
                                  </a></div>
                                <div class="topline-refill-btn-block"><a  class="topline-refill-btn tm">
                                  <div class="dashboard-info-stat-field dashboard-info-stat-field--wallet">
                                    <div class="dashboard-info-stat-field__content">
                                      <div class="dashboard-info-stat-field__title"> <span class="h">Right Team </span><br>
Total : <?php echo $right_team_total; ?><br>
Active : <?php echo $right_team_active; ?><br>
Business :<span class="amts"> $<?php echo number_format($right_team_business, 2); ?></span> <br></div>
                                    </div>
                                      <div class="acct_icon"><img src="assets/w1.png"></div>
                                  </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


</div>
<script src="./assets/cy/libs/jquery/jquery.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/libs/clipboard.min.js?vs=100"></script>
<script src="./assets/cy/js/main.js?vs=100"></script>
<script>
function copyReferralLink(link) {
  const tempInput = document.createElement("input");
  document.body.appendChild(tempInput);
  tempInput.value = link;
  tempInput.select();
  document.execCommand("copy");
  document.body.removeChild(tempInput);

  // Popup message
  const popup = document.createElement("div");
  popup.textContent = "Text Copied";
  Object.assign(popup.style, {
    position: "fixed",
    top: "20px",
    right: "20px",
    background: "#333",
    color: "#fff",
    padding: "10px 15px",
    borderRadius: "5px",
    fontSize: "14px",
    zIndex: 9999,
  });
  document.body.appendChild(popup);
  setTimeout(() => {
    popup.remove();
  }, 1500);
}
</script>
<script>
$(document).ready(function() {
    // Initialize clipboard for copy buttons
    $('.copy-btn').click(function () {
    const textToCopy = $(this).attr('data-clipboard-text');
    const tempInput = $('<input>');
    $('body').append(tempInput);
    tempInput.val(textToCopy).select();
    document.execCommand('copy');
    tempInput.remove();
    
    // Show popup
    const popup = $('<div>Text Copied</div>').css({
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: '#333',
      color: '#fff',
      padding: '10px 15px',
      borderRadius: '5px',
      zIndex: 9999,
      fontSize: '14px'
    });
    $('body').append(popup);
    setTimeout(() => { popup.fadeOut(500, () => popup.remove()); }, 1500);
  });
});
});
</script>
</body>
</html>
