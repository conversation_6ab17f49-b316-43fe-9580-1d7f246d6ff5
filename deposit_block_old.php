<?php 
$type = (isset($_GET['type']) && in_array($_GET['type'], array(1,2,3,4,5,6,7,8,12))) ? $_GET['type'] : 1;
$typearr = array(1 => 'USDT', 2 => 'LTC', 3 => 'DOGE', 4 => 'ETH', 5 => 'BCH', 6 => 'Dash', 7 => 'XRP', 8 => 'NEO', 12 => 'TRX');
$alt_color = array(1 => '#605CA8', 2 => '#0073B7', 3 => '#F39C12', 4 => '#605CA8', 5 => '#0073B7', 6 => '#F39C12', 7 => '#262D4E', 8 => '#FF851B', 12 => '#FF851B');
$type2 = $typearr[$type];
$title = "Add Fund by ".$type2.' (Network BEP20)';
$_is_dashboard = 1;
include_once 'header.php';
include_once '../lib/own_pay/own_pay.php';
user();
$uid = $_SESSION['userid'];
$user = get_user_details($uid);
if(empty($user->pay_address)){
     $wallet = generateNewWallet();
      $sql = "UPDATE user SET `pay_address` = '".$wallet['address']."', `pay_privatekey` = '".$wallet['privateKey']."' WHERE uid = '".$uid."'" ;
      my_query($sql);
      redirect('./deposit_block.php');
}
?>
<style>
    /* Binance-inspired theme */
    body, #page-wrapper {
        background-color: #0b0e11;
        color: #eaecef;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .content-header {
        display: none;
    }

    .deposit-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /* max-width: 1200px; */
    }

    /* Deposit Header */
    .deposit-header {
        background: linear-gradient(135deg, #181c27 0%, #0b0e11 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .deposit-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .deposit-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: radial-gradient(ellipse at bottom, rgba(240, 185, 11, 0.05), transparent 70%);
        pointer-events: none;
    }

    .deposit-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .deposit-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
        font-weight: 600;
    }

    .deposit-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
        font-size: 22px;
    }

    .wallet-balance {
        background: rgba(240, 185, 11, 0.1);
        border-radius: 8px;
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        border: 1px solid rgba(240, 185, 11, 0.2);
        position: relative;
        overflow: hidden;
    }

    .wallet-balance::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(240, 185, 11, 0.05) 0%, transparent 100%);
        pointer-events: none;
    }

    .balance-value {
        font-size: 24px;
        font-weight: 600;
        color: #f0b90b;
        display: flex;
        align-items: center;
    }

    .balance-value i {
        margin-right: 8px;
        font-size: 18px;
    }

    .balance-label {
        font-size: 14px;
        color: #848e9c;
        margin-top: 4px;
    }

    /* Deposit Card */
    .deposit-card {
        background: linear-gradient(135deg, #181c27 0%, #0b0e11 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .deposit-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .deposit-row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
    }

    .deposit-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;
    }

    @media (max-width: 992px) {
        .deposit-col {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 20px;
        }

        .deposit-header {
            padding: 20px;
        }

        .deposit-title {
            flex-direction: column;
            align-items: flex-start;
        }

        .wallet-balance {
            margin-top: 15px;
            width: 100%;
            align-items: flex-start;
        }
    }

    @media (max-width: 576px) {
        .deposit-wrapper {
            padding: 10px;
        }

        .card-body {
            padding: 20px 15px;
        }

        .address-card, .instructions-card {
            padding: 15px;
        }

        .payment-address {
            flex-direction: column;
            align-items: stretch;
        }

        .payment-address-text {
            margin-bottom: 10px;
            padding-right: 0;
            word-break: break-all;
            font-size: 12px;
        }

        .btn-copy {
            align-self: flex-end;
        }

        .qr-code-container img {
            max-width: 150px;
        }

        .btn-lg {
            padding: 12px 20px;
            font-size: 16px;
            width: 100%;
        }

        .step-number {
            width: 24px;
            height: 24px;
            font-size: 12px;
        }

        .step-title {
            font-size: 14px;
        }

        .step-description {
            font-size: 12px;
        }
    }

    .card-body {
        padding: 30px;
    }

    /* Address Card */
    .address-card {
        background: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        padding: 25px;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .address-card::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(ellipse at bottom right, rgba(240, 185, 11, 0.05), transparent 70%);
        pointer-events: none;
    }

    .address-title {
        font-size: 18px;
        color: #f0b90b;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .address-title i {
        margin-right: 10px;
    }

    /* Payment Address Styling */
    .payment-address {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        word-break: break-all;
        position: relative;
        overflow: hidden;
        flex-wrap: wrap;
    }

    .payment-address::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, rgba(240, 185, 11, 0.3), transparent);
    }

    .payment-address-text {
        flex: 1;
        font-family: monospace;
        font-size: 14px;
        color: #f0b90b;
        padding-right: 15px;
    }

    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        white-space: nowrap;
    }

    .btn-copy {
        background: rgba(255, 255, 255, 0.1);
        color: #eaecef;
        padding: 8px 16px;
        font-size: 14px;
    }

    .btn-copy:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .btn-copy.copied {
        background: rgba(14, 203, 129, 0.2);
        color: #0ecb81;
    }

    .btn-primary {
        background: linear-gradient(135deg, #f0b90b 0%, #fbda3c 100%);
        color: #000;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #e6b00a 0%, #f0c52e 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
    }

    .btn-lg {
        padding: 14px 28px;
        font-size: 18px;
    }

    /* QR Code */
    .qr-code-container {
        display: flex;
        justify-content: center;
        margin: 30px 0;
        position: relative;
    }

    .qr-code-container::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #f0b90b, transparent);
    }

    .qr-code-container::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #f0b90b, transparent);
    }

    .qr-code-container img {
        border-radius: 8px;
        padding: 15px;
        background: #fff;
        max-width: 180px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .action-container {
        text-align: center;
        margin-top: 30px;
    }

    /* Instructions Card */
    .instructions-card {
        background: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        padding: 25px;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .instructions-card::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(ellipse at bottom right, rgba(240, 185, 11, 0.05), transparent 70%);
        pointer-events: none;
    }

    .instructions-title {
        font-size: 18px;
        color: #f0b90b;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .instructions-title i {
        margin-right: 10px;
    }

    .instruction-step {
        display: flex;
        margin-bottom: 20px;
        position: relative;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background: rgba(240, 185, 11, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #f0b90b;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        font-weight: 600;
        color: #eaecef;
        margin-bottom: 5px;
    }

    .step-description {
        color: #848e9c;
        font-size: 14px;
        line-height: 1.5;
    }

    /* Modal Styling */
    .deposit-modal .modal-content {
        background: #181c27;
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        color: #eaecef;
        overflow: hidden;
    }

    .deposit-modal .modal-body {
        padding: 30px;
    }

    @media (max-width: 480px) {
        .deposit-modal .modal-body {
            padding: 20px 15px;
        }

        .custom-spinner {
            width: 40px;
            height: 40px;
            margin-bottom: 15px;
        }

        .monitoring-status h4, .monitoring-result h4 {
            font-size: 18px;
            margin-top: 15px;
        }

        .monitoring-status p, .monitoring-result p {
            font-size: 12px;
        }
    }

    /* Custom spinner animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .custom-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(240, 185, 11, 0.1);
        border-radius: 50%;
        border-top: 4px solid #f0b90b;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px auto;
    }

    .monitoring-status, .monitoring-result {
        text-align: center;
    }

    .monitoring-status h4, .monitoring-result h4 {
        margin-top: 20px;
        color: #fff;
        font-size: 20px;
    }

    .text-muted {
        color: #848e9c !important;
    }

    .text-success {
        color: #0ecb81 !important;
    }

    .text-warning {
        color: #f0b90b !important;
    }

    /* Additional Mobile Optimizations */
    @media (max-width: 480px) {
        .deposit-header {
            padding: 15px;
            margin-bottom: 15px;
        }

        .deposit-title h2 {
            font-size: 20px;
        }

        .balance-value {
            font-size: 20px;
        }

        .balance-label {
            font-size: 12px;
        }

        .address-title, .instructions-title {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .network-badge {
            font-size: 10px;
            padding: 4px 8px;
        }

        .payment-address {
            padding: 10px;
            margin-bottom: 20px;
        }

        .payment-address-text {
            font-size: 11px;
            line-height: 1.4;
        }

        .btn-copy {
            padding: 6px 12px;
            font-size: 12px;
            margin-top: 8px;
            width: 100%;
            justify-content: center;
        }

        .qr-code-container img {
            max-width: 120px;
            padding: 10px;
        }

        .instruction-step {
            margin-bottom: 15px;
        }

        .step-number {
            width: 20px;
            height: 20px;
            font-size: 10px;
            margin-right: 10px;
        }

        .step-title {
            font-size: 13px;
        }

        .step-description {
            font-size: 11px;
            line-height: 1.4;
        }

        .action-container {
            margin-top: 20px;
        }

        .btn-lg {
            padding: 10px 16px;
            font-size: 14px;
        }

        .text-muted {
            font-size: 11px !important;
            line-height: 1.4;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(240, 185, 11, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
        }
    }

    /* Network Badge */
    .network-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(240, 185, 11, 0.1);
        color: #f0b90b;
        border: 1px solid rgba(240, 185, 11, 0.2);
        margin-bottom: 15px;
    }

    .network-badge i {
        margin-right: 5px;
    }
</style>

<div class="deposit-wrapper">
    <!-- Deposit Header with Balance -->
    <div class="deposit-header">
        <div class="deposit-title">
            <h2><i class="fas fa-wallet"></i> Deposit USDT</h2>
            <div class="wallet-balance">
                <div class="balance-value">
                    <i class="fas fa-coins"></i>
                    <span class="count-number"><?php echo round($user->wallet_topup*1, 0);?></span>
                    <span class="currency"> <?php echo defined('SITE_CURRENCY') ? SITE_CURRENCY : 'USDT';?></span>
                </div>
                <div class="balance-label">Topup Wallet Balance</div>
            </div>
        </div>
    </div>

    <!-- Deposit Content -->
    <div class="deposit-row">
        <!-- Address Column -->
        <div class="deposit-col">
            <div class="deposit-card">
                <div class="card-body">
                    <div class="address-card">
                        <div class="address-title">
                            <i class="fas fa-qrcode"></i> Your USDT Deposit Address
                        </div>

                        <div class="network-badge">
                            <i class="fas fa-network-wired"></i> BSC Network (BEP-20)
                        </div>

                        <!-- Payment Address -->
                        <div class="payment-address">
                            <div class="payment-address-text" id="_address"><?php echo $user->pay_address;?></div>
                            <button class="btn btn-copy" onclick="CopyToClipboard2('_address');" id="_address_copy">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>

                        <!-- QR Code -->
                        <div class="qr-code-container">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=<?php echo $user->pay_address;?>" alt="QR Code" />
                        </div>

                        <!-- Action Button -->
                        <div class="action-container">
                            <button id="checkPayment" class="btn btn-primary btn-lg">
                                <i class="fas fa-sync-alt"></i> Check Payment Status
                            </button>
                            <p class="text-muted" style="margin-top: 15px; font-size: 14px;">
                                After sending USDT to this address, click the button above to verify your payment
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions Column -->
        <div class="deposit-col">
            <div class="deposit-card">
                <div class="card-body">
                    <div class="instructions-card">
                        <div class="instructions-title">
                            <i class="fas fa-info-circle"></i> How to Deposit USDT
                        </div>

                        <div class="instruction-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <div class="step-title">Copy Your Deposit Address</div>
                                <div class="step-description">
                                    Click the "Copy" button next to your USDT deposit address or scan the QR code with your wallet app.
                                </div>
                            </div>
                        </div>

                        <div class="instruction-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <div class="step-title">Send USDT to This Address</div>
                                <div class="step-description">
                                    Open your wallet app and send USDT to the copied address. Make sure you're using the BSC Network (BEP-20).
                                </div>
                            </div>
                        </div>

                        <div class="instruction-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <div class="step-title">Verify Your Deposit</div>
                                <div class="step-description">
                                    After sending, click the "Check Payment Status" button to verify your transaction. It may take a few minutes for the network to confirm your transaction.
                                </div>
                            </div>
                        </div>

                        <div class="instruction-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <div class="step-title">Start Using Your USDT</div>
                                <div class="step-description">
                                    Once your deposit is confirmed, your balance will be updated automatically and you can start using your USDT.
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 30px; padding: 15px; background: rgba(14, 203, 129, 0.1); border-radius: 8px; border: 1px solid rgba(14, 203, 129, 0.2);">
                            <div style="display: flex; align-items: center; margin-bottom: 10px; color: #0ecb81;">
                                <i class="fas fa-shield-alt" style="margin-right: 10px;"></i>
                                <strong>Security Tips</strong>
                            </div>
                            <ul style="color: #848e9c; font-size: 14px; padding-left: 20px; margin-bottom: 0;">
                                <li>Always double-check the address before sending</li>
                                <li>Start with a small test amount if this is your first deposit</li>
                                <li>Ensure you're using the correct network (BSC/BEP-20)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade deposit-modal" id="monitorModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="monitoring-status">
                    <div class="custom-spinner"></div>
                    <h4 id="statusText">Checking your payment...</h4>
                    <p id="statusDetails" class="text-muted">Please wait while we verify your transaction</p>
                </div>
                <div class="monitoring-result" style="display: none;">
                    <div id="resultIcon"></div>
                    <h4 id="resultText"></h4>
                    <p id="resultDetails" class="text-muted"></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once 'footer.php'; ?>

<script>
function CopyToClipboard2(containerid) {
    const text = document.getElementById(containerid).innerText;
    navigator.clipboard.writeText(text).then(() => {
        // Show a stylish notification
        const copyBtn = document.getElementById(containerid + '_copy');
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied';
        copyBtn.classList.add('copied');

        // Reset after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.classList.remove('copied');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ' + err);

        // Fallback to old method if clipboard API fails
        if (window.getSelection) {
            if (window.getSelection().empty) {
                window.getSelection().empty();
            } else if (window.getSelection().removeAllRanges) {
                window.getSelection().removeAllRanges();
            }
        } else if (document.selection) {
            document.selection.empty();
        }

        if (document.selection) {
            var range = document.body.createTextRange();
            range.moveToElementText(document.getElementById(containerid));
            range.select().createTextRange();
            document.execCommand("Copy");
        } else if (window.getSelection) {
            var range = document.createRange();
            range.selectNode(document.getElementById(containerid));
            window.getSelection().addRange(range);
            document.execCommand("Copy");
            $('#'+containerid+'_copy').text('Copied');
            $('#'+containerid+'_copy').addClass('btn-success');
            $('#'+containerid+'_copy').removeClass('btn-violet');
        }
    });
}

// Payment monitoring functionality
$('#checkPayment').click(function() {
    const walletAddress = $('#_address').text();
    startMonitoring(walletAddress);
});

const MAX_RETRIES = 4;  // Total of 2 minutes (4 * 30 seconds)
let retryCount = 0;

function startMonitoring(address) {
    // Show modal
    $('#monitorModal').modal({
        backdrop: 'static',
        keyboard: false
    });

    // Reset retry count when starting fresh
    retryCount = 0;

    // Update initial status
    updateStatusMessage(retryCount);

    // Start monitoring with timeout
    $.ajax({
        url: 'deposit_block2_model.php',
        method: 'POST',
        data: {
            address: address
        },
        timeout: 30000, // 30 second timeout
        success: function(response) {
            try {
                console.log(response);

                // Check if the response contains HTML error messages
                if (response.includes('<br />') || response.includes('<b>Warning</b>')) {
                    // Extract the JSON part if it exists
                    const jsonMatch = response.match(/\{"status":.*\}/);
                    if (jsonMatch) {
                        response = jsonMatch[0];
                    } else {
                        handleRetry(address);
                        return;
                    }
                }

                const result = JSON.parse(response);
                if (result.status === 'success') {
                    showSuccess();
                } else {
                    // Check if there's no balance
                    if (result.message && result.message.includes('No significant usdt balance found')) {
                        showError('No USDT balance found in this address. Please send USDT to the address first.');
                    } else {
                        handleRetry(address);
                    }
                }
            } catch (e) {
                console.error('Error parsing response:', e);
                showError('Invalid response from server. Please try again later.');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', status, error);
            if (status === 'timeout') {
                handleRetry(address);
            } else {
                showError('Connection error occurred. Please try again later.');
            }
        }
    });
}

function handleRetry(address) {
    retryCount++;

    if (retryCount >= MAX_RETRIES) {
        showError('Payment not detected after multiple attempts. Please try again or contact support if you have already sent the payment.');
        return;
    }

    updateStatusMessage(retryCount);

    // Wait 5 seconds before trying again
    setTimeout(function() {
        startMonitoring(address);
    }, 5000);
}

function updateStatusMessage(currentRetry) {
    const timeElapsed = currentRetry * 30;
    const messages = [
        'Checking your payment...',
        'Still checking... (30 seconds elapsed)',
        'Still checking... (1 minute elapsed)',
        'Final check... (1.5 minutes elapsed)'
    ];

    $('#statusText').text(messages[currentRetry] || 'Checking payment...');
    $('#statusDetails').html(`
        <p>This may take a few moments</p>
        <small class="text-muted">Attempt ${currentRetry + 1} of ${MAX_RETRIES}</small>
    `);
}

function showSuccess() {
    $('.monitoring-status').hide();
    $('.monitoring-result').show();
    $('#resultIcon').html('<i class="fas fa-check-circle text-success fa-3x"></i>');
    $('#resultText').text('Payment Confirmed!');
    $('#resultDetails').text('Your payment has been processed successfully');

    // Reload page after 3 seconds
    setTimeout(function() {
        window.location.reload();
    }, 1000);
}

function showError(message) {
    $('.monitoring-status').hide();
    $('.monitoring-result').show();
    $('#resultIcon').html('<i class="fas fa-exclamation-circle text-warning fa-3x"></i>');
    $('#resultText').text('Payment Not Found');

    // Add a help link for common issues
    const helpText = message.includes('No USDT balance') ?
        '<div style="margin-top: 10px; font-size: 13px; color: #848e9c;">Make sure you have sent USDT to this address using the BSC network.</div>' : '';

    $('#resultDetails').html(`
        ${message}<br>
        ${helpText}
        <button class="btn btn-primary mt-3" onclick="retryFromStart()">Check Again</button>
    `);
       setTimeout(function() {
        window.location.reload();
    }, 1000);
}

function retryFromStart() {
    const walletAddress = $('#_address').text();
    $('.monitoring-result').hide();
    $('.monitoring-status').show();
    startMonitoring(walletAddress);
}
</script>
