<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();
$title = "Transfer Report";

// Original query logic preserved
$query = "SELECT t.uid, t.amount, t.datetime, u.login_id, u.name, f.login_id as from_login_id, f.name as from_name, t.type, t.remark, t.from_uid, t.tamt FROM fund_transfer as t"
        . " LEFT JOIN user as u ON u.uid=t.uid"
        . " LEFT JOIN user as f ON f.uid=t.from_uid"
        . " WHERE t.uid='".$uid."' OR t.from_uid='".$uid."'"
        . " ORDER BY t.datetime DESC";
$result = my_query($query);
$i=0;
$fund_type = get_fund_type(1);

// Calculate total transfers
$total_transfers_query = "SELECT COUNT(*) as total FROM fund_transfer WHERE uid='$uid' OR from_uid='$uid'";
$total_transfers_result = my_query($total_transfers_query);
$total_transfers_row = mysqli_fetch_object($total_transfers_result);
$total_transfers = $total_transfers_row->total;

// Calculate total amount transferred
$total_amount_query = "SELECT SUM(amount) as total FROM fund_transfer WHERE uid='$uid' OR from_uid='$uid'";
$total_amount_result = my_query($total_amount_query);
$total_amount_row = mysqli_fetch_object($total_amount_result);
$total_amount = $total_amount_row->total ? $total_amount_row->total : 0;

// Get latest transfer date
$latest_transfer_query = "SELECT MAX(datetime) as latest FROM fund_transfer WHERE uid='$uid' OR from_uid='$uid'";
$latest_transfer_result = my_query($latest_transfer_query);
$latest_transfer_row = mysqli_fetch_object($latest_transfer_result);
$latest_transfer = $latest_transfer_row->latest ? date("d M, Y", strtotime($latest_transfer_row->latest)) : 'N/A';
?>

<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Transfer Report Styling */
.transfer-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.transfer-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

/* Statistics Cards */
.transfer-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
    color: #fff;
}

.stat-label {
    color: #848e9c;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.stat-change {
    color: #848e9c;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.stat-change i {
    margin-right: 5px;
    color: #17c964;
}

/* Transfer Report Card */
.transfer-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.transfer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-body {
    padding: 0;
}

/* Table Styling */
.transfer-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.transfer-table th {
    background: rgba(0, 0, 0, 0.2);
    color: #848e9c;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    position: sticky;
    top: 0;
}

.transfer-table td {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: #eaecef;
    font-size: 14px;
}

.transfer-table tr:hover td {
    background: rgba(255, 255, 255, 0.03);
}

.transfer-table tr:hover .amount-value {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: #17c964;
}

.amount-value {
    font-weight: 600;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.2s ease;
}

.user-badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: rgba(23, 201, 100, 0.1);
    color: #17c964;
    border: 1px solid rgba(23, 201, 100, 0.2);
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    background: rgba(1, 112, 239, 0.1);
    color: #0170ef;
    border: 1px solid rgba(1, 112, 239, 0.2);
}

.date-value {
    color: #848e9c;
    font-size: 13px;
}

.remark-text {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #eaecef;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #848e9c;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #17c964;
}

.empty-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #eaecef;
}

.empty-subtext {
    font-size: 14px;
    color: #848e9c;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .transfer-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-value {
        font-size: 20px;
    }

    .transfer-table {
        display: block;
        width: 100%;
    }

    .transfer-table thead {
        display: none;
    }

    .transfer-table tbody {
        display: block;
        width: 100%;
    }

    .transfer-table tr {
        display: block;
        width: 100%;
        margin-bottom: 15px;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        padding: 15px;
    }

    .transfer-table td {
        display: flex;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.03);
        align-items: center;
        text-align: left;
    }

    .transfer-table td:before {
        content: attr(data-label);
        width: 35%;
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding-right: 10px;
        flex-shrink: 0;
    }

    .transfer-table td:last-child {
        border-bottom: none;
    }

    .user-badge {
        font-size: 11px;
        padding: 4px 8px;
        max-width: none;
    }

    .remark-text {
        max-width: none;
        white-space: normal;
        word-break: break-word;
    }

    .card-body {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

@media screen and (max-width: 480px) {
    .stat-value {
        font-size: 18px;
    }

    .transfer-table td:before {
        width: 30%;
        font-size: 11px;
    }

    .empty-state {
        padding: 40px 15px;
    }

    .empty-icon {
        font-size: 36px;
    }

    .empty-text {
        font-size: 16px;
    }
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
        <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Dashboard</div>
          </div>
          </a> </li>
        <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
           <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-icon"><img src="assets/network.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
        </div>

        <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
           <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
        </div>
        <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate active">
          <div class="mobile-db-menu-link__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
           <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
           <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
       </div>

           <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="mobile-db-menu-icon"><img src="assets/withdraw.png"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
          </div>
          </a> </li>
        <div class="dropdown-container">
           <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
           <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
        </div>

          <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
          <div class="topline-logout-btn__icon"></div>
          <div class="mobile-db-menu-link__text-block">
            <div class="mobile-db-menu-link__text">Logout</div>
          </div>
          </a> </li>
      </ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>

<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy" >Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Life Time Cashback Income</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
          <!--  <div class="topline-user-panel-block">-->
          <!--    <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"-->
										<!--aria-expanded="false">-->
          <!--      <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--      <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>-->
          <!--      </a>-->
          <!--      <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Profile</div>-->
          <!--        </a> <a href="logout.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Logout</div>-->
          <!--        </a> </div>-->
          <!--    </div>-->
          <!--  </div>-->
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">View your complete fund transfer history with detailed analytics and transaction records.</div>

                          <!-- Transfer Statistics Cards -->
                          <div class="transfer-stats">
                              <div class="stat-card">
                                  <div class="stat-icon">
                                      <i class="fas fa-coins"></i>
                                  </div>
                                  <div class="stat-label">Total Amount</div>
                                  <div class="stat-value"><?php echo SITE_CURRENCY; ?><?php echo number_format($total_amount, 2); ?></div>
                                  <div class="stat-change">
                                      <i class="fas fa-arrow-up"></i>
                                      Lifetime Transfers
                                  </div>
                              </div>

                              <div class="stat-card">
                                  <div class="stat-icon">
                                      <i class="fas fa-sync-alt"></i>
                                  </div>
                                  <div class="stat-label">Total Transfers</div>
                                  <div class="stat-value"><?php echo $total_transfers; ?></div>
                                  <div class="stat-change">
                                      <i class="fas fa-exchange-alt"></i>
                                      Transactions
                                  </div>
                              </div>

                              <div class="stat-card">
                                  <div class="stat-icon">
                                      <i class="fas fa-clock"></i>
                                  </div>
                                  <div class="stat-label">Latest Transfer</div>
                                  <div class="stat-value" style="font-size: 16px;"><?php echo $latest_transfer; ?></div>
                                  <div class="stat-change">
                                      <i class="fas fa-calendar"></i>
                                      Last Activity
                                  </div>
                              </div>
                          </div>

                          <!-- Transfer History Card -->
                          <div class="transfer-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-history"></i> Transfer History</h3>
                              </div>
                              <div class="card-body">
                                  <?php
                                  // Reset result pointer for table display
                                  $result = my_query($query);
                                  $has_data = false;
                                  ?>
                                  <table class="transfer-table">
                                      <thead>
                                          <tr>
                                              <th>#</th>
                                              <th>From</th>
                                              <th>To</th>
                                              <th>Date & Time</th>
                                              <th>Amount (<?php echo SITE_CURRENCY;?>)</th>
                                              <th>Type</th>
                                              <th>Remark</th>
                                          </tr>
                                      </thead>
                                      <tbody>
                                          <?php
                                          $i = 0;
                                          while ($row = mysqli_fetch_object($result)){
                                              $has_data = true;
                                              $i++;?>
                                          <tr>
                                              <td data-label="#"><?php echo $i;?></td>
                                              <td data-label="From"><span class="user-badge"><?php echo $row->from_login_id." (".$row->from_name.")";?></span></td>
                                              <td data-label="To"><span class="user-badge"><?php echo $row->login_id." (".$row->name.")";?></span></td>
                                              <td data-label="Date" class="date-value"><?php echo date("d M, Y h:i A", strtotime($row->datetime));?></td>
                                              <td data-label="Amount"><span class="amount-value"><?php echo number_format($row->amount, 2);?></span></td>
                                              <td data-label="Type"><span class="type-badge"><?php echo $fund_type[$row->type];?></span></td>
                                              <td data-label="Remark"><span class="remark-text"><?php echo $row->remark;?></span></td>
                                          </tr>
                                          <?php }

                                          if (!$has_data): ?>
                                          <tr>
                                              <td colspan="7">
                                                  <div class="empty-state">
                                                      <div class="empty-icon">
                                                          <i class="fas fa-exchange-alt"></i>
                                                      </div>
                                                      <div class="empty-text">No transfers found</div>
                                                      <div class="empty-subtext">You haven't made any fund transfers yet. Start transferring funds to see your history here.</div>
                                                  </div>
                                              </td>
                                          </tr>
                                          <?php endif; ?>
                                      </tbody>
                                  </table>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Transfer Report page initialized');

            // Add click handlers for user badges to show full names
            $('.user-badge').on('click', function() {
                const fullText = $(this).text();
                if (fullText.length > 20) {
                    alert('Full Name: ' + fullText);
                }
            });

            // Add click handlers for remark text to show full content
            $('.remark-text').on('click', function() {
                const fullText = $(this).text();
                if (fullText.length > 30) {
                    alert('Full Remark: ' + fullText);
                }
            });

            // Add search functionality (if needed in future)
            $('#searchTransfers').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('.transfer-table tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    if (rowText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        });
    }

    // Initialize the app
    initializeApp();
})();

// Add table sorting functionality
function sortTable(columnIndex) {
    const table = document.querySelector('.transfer-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Skip if no data rows
    if (rows.length <= 1) return;

    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // Handle numeric values (amount column)
        if (columnIndex === 4) {
            const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
            return isAscending ? aNum - bNum : bNum - aNum;
        }

        // Handle date values
        if (columnIndex === 3) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return isAscending ? aDate - bDate : bDate - aDate;
        }

        // Handle text values
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

// Add export functionality
function exportTransfers() {
    const table = document.querySelector('.transfer-table');
    const rows = table.querySelectorAll('tr');
    let csv = '';

    rows.forEach(row => {
        const cells = row.querySelectorAll('th, td');
        const rowData = Array.from(cells).map(cell => {
            return '"' + cell.textContent.trim().replace(/"/g, '""') + '"';
        });
        csv += rowData.join(',') + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'transfer_history.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}
</script>

</body>
</html>
