<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();
$title = "Tree View";

$childs = get_single_dimensional(get_child_levels($uid, 'yes'));
if (isset($_GET['login_id'])) {
    $login_id = tres($_GET['login_id']);
    $no = registeredUserId($login_id);
    if ($no != 0) {
        $uid = $no;
    }
}
if (isset($_GET['no'])) {
    $no = tres($_GET['no']);
    if ($no != 0) {
        $uid = $no;
    }
}

if (!in_array($uid, $childs)) {
    redirect('tree_view.php');
}

$userF = get_user_details($uid);
$rsS = my_query("SELECT * FROM user WHERE placement_id = '$uid'");
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Tree View Styling */
.tree-container {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.tree-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.tree-search-form {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.tree-search-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #eaecef;
    border-radius: 6px;
    padding: 10px 15px;
}

.tree-search-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(23, 201, 100, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(23, 201, 100, 0.25);
    color: #fff;
}

.tree-search-form .form-control::placeholder {
    color: #848e9c;
}

.tree-search-btn {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    border: none;
    color: #fff;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tree-search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(23, 201, 100, 0.3);
    color: #fff;
}

.tree-view-area {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 30px;
    min-height: 400px;
    position: relative;
}

.tree-node {
    position: relative;
    display: inline-block;
    margin: 15px;
    text-align: center;
}

.tree-user {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    min-width: 120px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.tree-user:hover {
    transform: translateY(-5px);
    border-color: rgba(23, 201, 100, 0.5);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.tree-user.active {
    border-color: #17c964;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.1) 0%, rgba(1, 112, 239, 0.1) 100%);
}

.tree-user.inactive {
    border-color: #f6465d;
    background: linear-gradient(135deg, rgba(246, 70, 93, 0.1) 0%, rgba(246, 70, 93, 0.05) 100%);
}

.tree-user.blocked {
    border-color: #848e9c;
    background: linear-gradient(135deg, rgba(132, 142, 156, 0.1) 0%, rgba(132, 142, 156, 0.05) 100%);
}

.tree-user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

.tree-user.inactive .tree-user-avatar {
    background: linear-gradient(114deg, #f6465d 0%, #ff6b7a 100%);
}

.tree-user.blocked .tree-user-avatar {
    background: linear-gradient(114deg, #848e9c 0%, #9ca3af 100%);
}

.tree-user-id {
    font-size: 12px;
    color: #848e9c;
    margin-bottom: 5px;
}

.tree-user-name {
    font-size: 14px;
    font-weight: 600;
    color: #eaecef;
    margin-bottom: 8px;
}

.tree-user-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.tree-user-status.active {
    background: rgba(23, 201, 100, 0.2);
    color: #17c964;
}

.tree-user-status.inactive {
    background: rgba(246, 70, 93, 0.2);
    color: #f6465d;
}

.tree-user-status.blocked {
    background: rgba(132, 142, 156, 0.2);
    color: #848e9c;
}

.tree-new-user {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
    border: 2px dashed rgba(23, 201, 100, 0.3);
    border-radius: 12px;
    padding: 15px;
    min-width: 120px;
    min-height: 140px;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: #848e9c;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tree-new-user::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tree-new-user:hover::before {
    opacity: 1;
}

.tree-new-user:hover {
    border-color: rgba(23, 201, 100, 0.6);
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.08) 0%, rgba(1, 112, 239, 0.08) 100%);
    color: #17c964;
    text-decoration: none;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(23, 201, 100, 0.15);
}

.tree-new-user-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.1) 0%, rgba(1, 112, 239, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.tree-new-user:hover .tree-new-user-icon {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    color: #fff;
    transform: scale(1.1);
}

.tree-new-user-icon i {
    font-size: 20px;
    transition: all 0.3s ease;
}

.tree-new-user-text {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 3px;
    text-align: center;
}

.tree-new-user-position {
    font-size: 10px;
    color: #848e9c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.tree-connection-line {
    position: absolute;
    background: linear-gradient(90deg, #17c964, #0170ef);
    height: 2px;
    z-index: 1;
}

.tree-connection-vertical {
    position: absolute;
    background: linear-gradient(180deg, #17c964, #0170ef);
    width: 2px;
    z-index: 1;
}

/* Tree Tooltip */
.tree-tooltip {
    position: absolute;
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    min-width: 250px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.tree-user:hover .tree-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(-10px);
}

.tree-tooltip h4 {
    color: #fff;
    margin-bottom: 10px;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tree-tooltip-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
}

.tree-tooltip-stat {
    text-align: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.tree-tooltip-stat-label {
    font-size: 10px;
    color: #848e9c;
    text-transform: uppercase;
    margin-bottom: 3px;
}

.tree-tooltip-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #eaecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tree-container {
        padding: 20px;
    }

    .tree-view-area {
        padding: 20px;
    }

    .tree-user {
        min-width: 100px;
        padding: 12px;
    }

    .tree-user-avatar {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .tree-tooltip {
        min-width: 200px;
        padding: 12px;
    }
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits active">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="direct_referral.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_level.php">Level Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits active">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_level.php">Level Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
            <div class="topline-user-panel-block">
              <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>
                </a>
                <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                  <div class="user-link__text">Profile</div>
                  </a> <a href="logout.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>
                  <div class="user-link__text">Logout</div>
                  </a> </div>
              </div>
            </div>
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%;">
                        <div class="refill-form-block">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">Visualize your network structure and team hierarchy in an interactive tree format.</div>

                          <!-- Tree Container -->
                          <div class="tree-container">
                              <!-- Search Form -->
                              <div class="tree-search-form">
                                  <form class="form-horizontal" id="form-validate" action="tree_view.php" method="get">
                                      <div class="row align-items-end">
                                          <div class="col-md-8">
                                              <label for="login_id" style="color: #eaecef; margin-bottom: 8px; font-size: 14px;">Search User by ID</label>
                                              <input class="form-control" type="text" id="login_id" name="no" maxlength="50" required="required" placeholder="Enter User ID to view their tree..." />
                                          </div>
                                          <div class="col-md-4">
                                              <button type="submit" class="tree-search-btn w-100" id="submit">
                                                  <i class="fas fa-search me-2"></i>Search Tree
                                              </button>
                                          </div>
                                      </div>
                                  </form>
                              </div>

                              <!-- Tree View Area -->
                              <div class="tree-view-area">
                                  <div class="text-center">
                                      <!-- Root User -->
                                      <div class="tree-node">
                                          <div class="tree-user <?php echo get_user_status_class($userF); ?>" onclick="window.location.href='tree_view.php?no=<?php echo $uid; ?>'">
                                              <div class="tree-user-avatar">
                                                  <?php echo strtoupper(substr($userF->name ?? 'U', 0, 1)); ?>
                                              </div>
                                              <div class="tree-user-id">#<?php echo $userF->uid; ?></div>
                                              <div class="tree-user-name"><?php echo $userF->name ?? 'User'; ?></div>
                                              <div class="tree-user-status <?php echo get_user_status($userF); ?>">
                                                  <?php echo get_user_status($userF); ?>
                                              </div>

                                              <!-- Enhanced Tooltip -->
                                              <div class="tree-tooltip">
                                                  <h4><i class="fas fa-user me-2"></i>Member Details</h4>
                                                  <div style="color: #eaecef; margin-bottom: 10px;">
                                                      <div style="margin-bottom: 5px;"><strong>DOJ:</strong> <?php echo date("d M, Y", strtotime($userF->datetime)); ?></div>
                                                      <div style="margin-bottom: 5px;"><strong>Sponsor:</strong> <?php echo $userF->refer_id; ?></div>
                                                      <div style="margin-bottom: 5px;"><strong>Package:</strong> $<?php echo number_format($userF->topup ?? 0, 2); ?></div>
                                                  </div>
                                                  <div class="tree-tooltip-stats">
                                                      <div class="tree-tooltip-stat">
                                                          <div class="tree-tooltip-stat-label">Left Team</div>
                                                          <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($userF->uid, 'L'); ?></div>
                                                      </div>
                                                      <div class="tree-tooltip-stat">
                                                          <div class="tree-tooltip-stat-label">Right Team</div>
                                                          <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($userF->uid, 'R'); ?></div>
                                                      </div>
                                                      <div class="tree-tooltip-stat">
                                                          <div class="tree-tooltip-stat-label">Left BV</div>
                                                          <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($userF->uid, 'L'), 0); ?></div>
                                                      </div>
                                                      <div class="tree-tooltip-stat">
                                                          <div class="tree-tooltip-stat-label">Right BV</div>
                                                          <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($userF->uid, 'R'), 0); ?></div>
                                                      </div>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>

                                      <!-- Connection Line -->
                                      <div style="margin: 20px 0;">
                                          <div style="width: 2px; height: 30px; background: linear-gradient(180deg, #17c964, #0170ef); margin: 0 auto;"></div>
                                      </div>

                                      <!-- Child Nodes -->
                                      <?php renderModernTree($uid, 2); ?>
                                  </div>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Tree View page initialized');

            // Add smooth scrolling for tree navigation
            $('.tree-user').on('click', function(e) {
                if (!$(e.target).closest('a').length) {
                    e.preventDefault();
                    // Add any additional tree interaction logic here
                }
            });

            // Enhanced tooltip positioning
            $('.tree-user').hover(
                function() {
                    var tooltip = $(this).find('.tree-tooltip');
                    var rect = this.getBoundingClientRect();
                    var viewportWidth = window.innerWidth;

                    // Adjust tooltip position based on viewport
                    if (rect.left < 200) {
                        tooltip.css('left', '120px');
                    } else if (rect.right > viewportWidth - 200) {
                        tooltip.css('left', '-270px');
                    } else {
                        tooltip.css('left', '-125px');
                    }
                }
            );
        });
    }

    // Initialize the app
    initializeApp();
})();
</script>

</body>
</html>

<?php

function get_img_clr($user) {
    $black = 3;
    $green = 2;
    $red = 4;
    if ($user->status == 1) {
        $img = $black;
    } elseif ($user->topup > 0) {
        $img = $green;
    } else {
        $img = $red;
    }
    return $img;
}

function get_user_status($user) {
    if ($user->status == 1) {
        return 'blocked';
    } elseif ($user->topup > 0) {
        return 'active';
    } else {
        return 'inactive';
    }
}

function get_user_status_class($user) {
    if ($user->status == 1) {
        return 'blocked';
    } elseif ($user->topup > 0) {
        return 'active';
    } else {
        return 'inactive';
    }
}
?>

<?php

function getTreeDescription($uid, $date, $refer_id) {
    // This function is kept for backward compatibility but not used in modern design
}

function renderModernTree($uid, $__i = 1, $__k = 0) {
    $__k++;
    $rs = my_query("SELECT * FROM user WHERE placement_id = '$uid'");
    $num = my_num_rows($rs);
    $parr = ['L' => 1, 'R' => 2];

    if ($num > 0 || count($parr) > 0) {
        echo '<div class="row justify-content-center" style="margin-top: 30px;">';

        // Process existing users
        if ($num) {
            while ($row = my_fetch_object($rs)) {
                $p = $row->position;
                $colClass = ($p == 'R') ? 'col-md-6 text-end' : 'col-md-6 text-start';
                unset($parr[$p]);
                ?>
                <div class="<?php echo $colClass; ?>">
                    <div class="tree-node">
                        <div class="tree-user <?php echo get_user_status_class($row); ?>" onclick="window.location.href='tree_view.php?no=<?php echo $row->uid; ?>'">
                            <div class="tree-user-avatar">
                                <?php echo strtoupper(substr($row->name ?? 'U', 0, 1)); ?>
                            </div>
                            <div class="tree-user-id">#<?php echo $row->uid; ?></div>
                            <div class="tree-user-name"><?php echo $row->name ?? 'User'; ?></div>
                            <div class="tree-user-status <?php echo get_user_status($row); ?>">
                                <?php echo get_user_status($row); ?>
                            </div>

                            <!-- Enhanced Tooltip -->
                            <div class="tree-tooltip">
                                <h4><i class="fas fa-user me-2"></i>Member Details</h4>
                                <div style="color: #eaecef; margin-bottom: 10px;">
                                    <div style="margin-bottom: 5px;"><strong>DOJ:</strong> <?php echo date("d M, Y", strtotime($row->datetime)); ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Sponsor:</strong> <?php echo $row->refer_id; ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Package:</strong> $<?php echo number_format($row->topup ?? 0, 2); ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Position:</strong> <?php echo $row->position; ?></div>
                                </div>
                                <div class="tree-tooltip-stats">
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Left Team</div>
                                        <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($row->uid, 'L'); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Right Team</div>
                                        <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($row->uid, 'R'); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Left BV</div>
                                        <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($row->uid, 'L'), 0); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Right BV</div>
                                        <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($row->uid, 'R'), 0); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($__k < $__i) { ?>
                            <!-- Connection Line -->
                            <div style="margin: 15px 0;">
                                <div style="width: 2px; height: 20px; background: linear-gradient(180deg, #17c964, #0170ef); margin: 0 auto;"></div>
                            </div>
                            <?php renderModernTree($row->uid, $__i, $__k); ?>
                        <?php } ?>
                    </div>
                </div>
                <?php
            }
        }

        // Process empty positions
        foreach ($parr as $k => $v) {
            $colClass = ($k == 'R') ? 'col-md-6 text-end' : 'col-md-6 text-start';
            ?>
            <div class="<?php echo $colClass; ?>">
                <div class="tree-node">
                    <a href="tree_register.php?placement_id=<?php echo $uid; ?>&position=<?php echo $k; ?>" target="_blank" class="tree-new-user">
                        <div class="tree-new-user-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="tree-new-user-text">Add Member</div>
                        <div class="tree-new-user-position"><?php echo $k; ?> Position</div>
                    </a>
                </div>
            </div>
            <?php
        }

        echo '</div>';
    }
}
?>

