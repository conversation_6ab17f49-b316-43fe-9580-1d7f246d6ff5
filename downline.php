<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();
$title = "Team Downline";
$i=0;
$j=0;
$child_levels = get_child_levels($uid, $with='yes');

// Calculate team statistics properly
$total_team = 0;
$total_active = 0;
$total_inactive = 0;
$total_earnings = 0;

foreach($child_levels as $level) {
    if(!empty($level)) {
        $uids = implode(",", $level);
        if($uids) {
            // Get user details for this level
            $level_query = "SELECT uid, topup FROM user WHERE uid IN ($uids)";
            $level_result = my_query($level_query);

            while($level_row = mysqli_fetch_object($level_result)) {
                $total_team++;
                $total_earnings += ($level_row->topup ? $level_row->topup : 0);

                if($level_row->topup > 0) {
                    $total_active++;
                } else {
                    $total_inactive++;
                }
            }
        }
    }
}

$total_levels = count($child_levels);


?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Team Downline Styling */
.team-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.team-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.team-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.team-title h2 {
    font-size: 24px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.team-title h2 i {
    margin-right: 12px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.stat-label {
    font-size: 14px;
    color: #848e9c;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.stat-label i {
    margin-right: 8px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #eaecef;
    display: flex;
    align-items: baseline;
}

.stat-value.earnings {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

    .stat-trend {
        font-size: 12px;
        margin-left: 8px;
        display: flex;
        align-items: center;
    }

    .trend-up {
        color: #0ecb81;
    }

    .trend-down {
        color: #f6465d;
    }

/* Team Card */
.team-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .team-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .team-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .team-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .team-table tr:last-child td {
        border-bottom: none;
    }

    .team-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-approved {
        background-color: rgba(14, 203, 129, 0.1);
        color: #0ecb81;
        border: 1px solid rgba(14, 203, 129, 0.2);
    }

    .status-pending {
        background-color: rgba(246, 70, 93, 0.1);
        color: #f6465d;
        border: 1px solid rgba(246, 70, 93, 0.2);
    }

    /* User Info */
    .user-info {
        display: flex;
        align-items: center;
    }

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    font-weight: bold;
    margin-right: 10px;
}

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .user-name {
        font-weight: 500;
        color: #fff;
    }

    .user-id {
        font-size: 12px;
        color: #848e9c;
    }

/* Level Badge */
.level-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    color: #fff;
    font-weight: bold;
    font-size: 12px;
}

/* Package Value */
.package-value {
    font-weight: 600;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Level filtering */
.level-row {
    transition: all 0.3s ease;
}

.level-row.hidden {
    display: none !important;
}

    /* Pagination */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding: 10px;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        overflow: hidden;
    }

    .pagination li {
        border-right: 1px solid rgba(255, 255, 255, 0.05);
    }

    .pagination li:last-child {
        border-right: none;
    }

    .pagination a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        color: #eaecef;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
    }

    .pagination a:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .pagination .active a {
        background: rgba(240, 185, 11, 0.2);
        color: #f0b90b;
    }

    /* Empty State */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #848e9c;
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: rgba(240, 185, 11, 0.3);
    }

    .empty-text {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .team-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .team-header {
            padding: 20px;
        }

        .team-table {
            display: block;
            overflow-x: auto;
        }

        .team-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits active">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_level.php">Level Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>

<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits active">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_level.php">Level Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
            <div class="topline-user-panel-block">
              <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>
                </a>
                <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                  <div class="user-link__text">Profile</div>
                  </a> <a href="logout.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>
                  <div class="user-link__text">Logout</div>
                  </a> </div>
              </div>
            </div>
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">View and manage your complete team network and downline structure.</div>

                          <!-- Team Header with Stats -->
                          <div class="team-header">
                              <div class="team-title">
                                  <h2><i class="fas fa-users"></i> My Team</h2>
                              </div>
                              <div class="team-stats">
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-user-friends"></i> Team Members</div>
                                      <div class="stat-value"><?php echo $total_team; ?></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-check-circle"></i> Active Members</div>
                                      <div class="stat-value earnings"><?php echo $total_active; ?></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-times-circle"></i> Inactive Members</div>
                                      <div class="stat-value"><?php echo $total_inactive; ?></div>
                                  </div>
                              </div>
                          </div>

                          <!-- Team List Card -->
                          <div class="team-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-list"></i> Team Members</h3>
                                  <div class="card-header-actions">
                                      <select class="card-filter">
                                          <option value="all">All Levels</option>
                                          <?php for ($l = 0; $l < count($child_levels); $l++) { ?>
                                              <option value="<?php echo $l; ?>">Level <?php echo $l; ?></option>
                                          <?php } ?>
                                      </select>
                                  </div>
                              </div>
                              <div class="card-body">
                                  <table class="team-table">
                                      <thead>
                                          <tr>
                                              <th width="50">#</th>
                                              <th>Member</th>
                                              <th>Joined</th>
                                              <th>Placement</th>
                                              <th>Package</th>
                                              <th>Level</th>
                                          </tr>
                                      </thead>
                                      <tbody>
                                          <?php
                                          $has_members = false;
                                          $j = 0; // Reset level counter
                                          foreach ($child_levels as $key => $child_level){
                                              $current_level = $j; // Store current level
                                              $uids = implode(" , ", $child_level);
                                              if(!$uids){$uids = 0;}
                                              $query = "SELECT u.uid, u.login_id, u.name, u.mobile, u.datetime, s.uid as sponsoruid, s.login_id as sponsor, p.uid as placementuid, p.login_id as placement, u.position, u.package, u.topup FROM user as u"
                                                  . " LEFT JOIN user as s ON s.uid=u.refer_id"
                                                  . " LEFT JOIN user as p ON p.uid=u.placement_id"
                                                  . " WHERE u.uid IN ($uids)";

                                              $result = my_query($query);
                                              while ($row = my_fetch_object($result)){
                                              $has_members = true;
                                              $i++;?>
                                              <tr class="level-row" data-level="<?php echo $current_level; ?>">
                                                  <td><?php echo $i;?></td>
                                                  <td>
                                                      <div class="user-info">
                                                          <div class="user-avatar"><?php echo substr($row->name, 0, 1); ?></div>
                                                          <div class="user-details">
                                                              <span class="user-name"><?php echo $row->name;?></span>
                                                              <span class="user-id"><?php echo $row->login_id;?></span>
                                                          </div>
                                                      </div>
                                                  </td>
                                                  <td><?php echo date("d M, Y", strtotime($row->datetime));?></td>
                                                  <td>
                                                      <div class="user-details">
                                                          <span class="user-id"><?php echo $row->placementuid;?></span>
                                                      </div>
                                                  </td>
                                                  <td><span class="package-value"><?php echo number_format($row->topup*1, 2);?></span></td>
                                                  <td><div class="level-badge"><?php echo $current_level;?></div></td>
                                              </tr>
                                              <?php }
                                              $j++; // Increment level counter
                                          }

                                              // Show empty state if no members found
                                              if (!$has_members): ?>
                                              <tr>
                                                  <td colspan="6">
                                                      <div class="empty-state" style="text-align: center; padding: 40px 20px;">
                                                          <div class="empty-icon" style="font-size: 48px; color: #848e9c; margin-bottom: 15px;"><i class="fas fa-users"></i></div>
                                                          <div class="empty-text" style="font-size: 18px; color: #eaecef; margin-bottom: 8px;">You don't have any team members yet</div>
                                                          <div class="empty-subtext" style="font-size: 14px; color: #848e9c;">Start building your team by sharing your referral link with potential members</div>
                                                      </div>
                                                  </td>
                                              </tr>
                                              <?php endif; ?>
                                      </tbody>
                                  </table>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-countup/1.0.0/jquery.countup.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Team Downline page initialized');

            // Level filtering functionality
            $('.card-filter').on('change', function() {
                var selectedLevel = $(this).val();

                if (selectedLevel === 'all') {
                    // Show all rows
                    $('.level-row').removeClass('hidden').show();
                } else {
                    // Hide all rows first
                    $('.level-row').addClass('hidden').hide();

                    // Show only rows matching the selected level
                    $('.level-row[data-level="' + selectedLevel + '"]').removeClass('hidden').show();
                }

                // Update row numbers
                updateRowNumbers();
            });

            function updateRowNumbers() {
                $('.level-row:visible').each(function(index) {
                    $(this).find('td:first').text(index + 1);
                });
            }
        });
    }

    // Initialize the app
    initializeApp();
})();
</script>

</body>
</html>