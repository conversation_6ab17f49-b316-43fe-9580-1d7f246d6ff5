<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>
<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href="account.php" class="mobile-db-menu-link mobile-db-menu-link--dashboard active">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="change-password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="balance.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="transactions.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="activation.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="activation_report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="left-network.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="right-network.php">Right Direct</a></span> </li>
    <li class="hidden"><span class="capa"><a href="total-team.php">Total Team</a></span> </li>
    <li class="hidden"><span class="capa"><a href="tree.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="non-working-bonus.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="matching-bonus.php">Matching Bonus</a></span> </li>

    <li class="hidden"><span class="capa"><a href="reward-bonus.php">Reward Bonus</a></span> </li>
    <li class="hidden"><span class="capa"><a href="lottery-bonus.php">Lottery Bonus</a></span> </li>
    <li class="hidden"><span class="capa"><a href="game-bonus.php">Game Bonus</a></span> </li>
    <li class="hidden"><span class="capa"><a href="bonus-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"><a href="activation-wallet.php">Activation Wallet</a></span> </li>
    <li class="hidden"><span class="capa"><a href="cashback-wallet.php">Cashback Wallet</a></span> </li>
    <li class="hidden"><span class="capa"><a href="earning-wallet.php">Earning Wallet</a></span> </li>
    <li class="hidden"><span class="capa"><a href="gaming-wallet.php">Gaming Wallet</a></span> </li>
    <li class="hidden"><span class="capa"><a href="wallet-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="withdrawal.php" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="withdrawal-report.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="buy-lottery.php" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="lottery-reports.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden"><span class="capa"> <a href="play-games.php" class="openMod" data-modal="buy">Play Games</a> </span> </li>
    <li class="hidden"><span class="capa"><a href="games-reports.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="promo.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Promo Stuff </div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Support Ticket </div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href="account.php" class="db-sidemenu-link db-sidemenu-link--dashboard active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>

            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="change-password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="balance.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="transactions.php">Report</a></span> </li>
      </div>

<div class="refill-block">
                    <div class="refill">
                      <div class="refill-left">
                        <div class="refill-info-block">
                          <div class="refill-info">
                            <h1> Deposit </h1>
                            <div class="refill-info-descr"> Deposit  your account, invest, play, earn, and enjoy the experience. </div>
                            <div class="account-info-balance-block">
                              <div class="account-info-balance-title"> Available balance </div>
                              <div class="account-info-balance-items-block">
                                <div class="account-info-balance-items">
                                  <div class="account-info-balance-item-wrapper">
                                    <div class="account-info-balance-item">
                                      <div class="account-info-balance-item-left">
                                        <div class="account-info-balance-item-currency">
                                          <div class="account-info-balance-item-currency__icon"> <img class="image" src="assets/cy/images/svg/payment/usdt.svg" alt=""> </div>
                                          <div class="account-info-balance-item-currency__amount userInfoBalance" data-currency="usdt"> 100000 </div>
                                        </div>
                                      </div>
                                      <div class="account-info-balance-item-right">
                                        <div class="account-info-balance-item-abbr"> USDT </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="refill-right">
                        <div class="refill-content-block">
                          <div class="refill-form-block">
                            <div class="h2 refill-form-title"> Deposit  your account </div>
                            <div class="refill-form form blockTopupBalance">
                              <div class="field-block">
                                <div class="field-title-block">
                                  <div class="field-title"> Asset <span class="field-required-star">*</span> </div>
                                </div>
                                <div class="field field--select">
                                  <div class="dropdown bootstrap-select select-currency">
                                    <select name="currency" class="select-currency">
                                      <option data-content="&lt;div class=&quot;select-currency-list-item&quot;&gt;&lt;div class=&quot;select-currency-list-item__left&quot;&gt;&lt;div class=&quot;select-currency-list-item__icon select-currency-list-item__icon--usdt&quot;&gt;&lt;img class=&quot;image&quot; src=&quot;assets/cy/images/svg/payment/usdt.svg&quot; alt=&quot;&quot;&gt;&lt;/div&gt;&lt;div class=&quot;select-currency-list-item__arrow&quot;&gt;&lt;/div&gt;&lt;/div&gt;&lt;div class=&quot;select-currency-list-item__right&quot;&gt;&lt;div class=&quot;select-currency-list-item__title&quot;&gt;USDT&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" value="usdt">USDT</option>
                                    </select>
                                    <div class="dropdown-menu ">
                                      <div class="inner show" role="listbox" id="bs-select-4" tabindex="-1">
                                        <ul class="dropdown-menu inner show" role="presentation">
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="field-block">
                                <div class="field-title-block">
                                  <div class="field-title"> Blockchain <span class="field-required-star">*</span> </div>
                                </div>
                                <div class="field field--select field--protocol">
                                  <div class="field-icon"></div>
                                  <div class="dropdown bootstrap-select select">
                                    <select name="payment" class="select">
                                      <option data-currency="usdt" data-content="&lt;div class=&quot;select-item&quot;&gt;&lt;div class=&quot;select-item-text&quot;&gt;BSC (BEP20)&lt;/div&gt;&lt;/div&gt;" value="tether_bep-20_usdt">BSC </option>

                                    </select>
                                    <div class="dropdown-menu ">
                                      <div class="inner show" role="listbox" id="bs-select-5" tabindex="-1">
                                        <ul class="dropdown-menu inner show" role="presentation">
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="field-message-block sendOnlyNetworkInfo" data-message="Send [currency] on the [network] blockchain, otherwise your funds will be lost."> <br>
                                <div class="field-message">
                                  <div class="field-message__icon"></div>
                                  <div class="field-message__text">Send <strong>USDT</strong> on the <strong>BSC</strong> blockchain, otherwise your funds will be lost.</div>
                                </div>
                              </div>
                              <div class="field-block">
                                <div class="field-title-block">
                                  <div class="field-title"> Address </div>
                                </div>
                                <div class="field field--textarea field--have-icon field--wallet">
                                  <div class="field-icon"></div>
                                  <textarea name="wallet" placeholder="" data-waiting="Waiting for address..." style="overflow: hidden; overflow-wrap: break-word; height: 56px;" id="_address">******************************************</textarea>
                                  <div class="field-right-panel-block">
                                    <div class="field-right-panel">
                                      <div class="copy-field-btn-block">
                                        <button class="copy-field-btn purple-btn" onclick="CopyToClipboard2('_address');" id="_address_copy" data-clipboard-text="******************************************" aria-label="Successfully"></button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="field-block d-none">
                                <div class="field-title-block">
                                  <div class="field-title"> Destination tag </div>
                                </div>
                                <div class="field field--input field--have-icon field--destination-tag">
                                  <div class="field-icon"></div>
                                  <input type="text" name="tag" placeholder="" autocomplete="off" value="">
                                  <div class="field-right-panel-block">
                                    <div class="field-right-panel">
                                      <div class="copy-field-btn-block">
                                        <button class="copy-field-btn purple-btn" data-clipboard-text="" aria-label="Successfully"></button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="field-message-block sendConfirmations" data-message="Your deposit will be credited after [conf] network confirmation(s). This usually takes 1-15 minutes."> <br>
                                <div class="field-message">
                                  <div class="field-message__icon"></div>
                                  <div class="field-message__text">Your deposit will be credited after <strong>10</strong> network confirmation(s). This usually takes 1-15 minutes.</div>
                                </div>
                              </div>

                              <!-- QR Code Section -->
                              <div class="field-block" style="text-align: center; margin-top: 20px;">
                                <div class="field-title-block">
                                  <div class="field-title">QR Code</div>
                                </div>
                                <div style="display: flex; justify-content: center; margin: 20px 0;">
                                  <img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&amp;data=******************************************" alt="QR Code" style="border-radius: 8px; padding: 15px; background: #fff; max-width: 180px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                                </div>
                              </div>

                              <!-- Check Payment Button -->
                              <div class="field-block" style="text-align: center; margin-top: 20px;">
                                <button id="checkPayment" class="green-gr-btn send-btn" style="width: 100%;">
                                  <div class="send-btn__text"><i class="fas fa-sync-alt"></i> Check Payment Status</div>
                                  <div class="send-btn__icon"></div>
                                </button>
                                <p style="margin-top: 15px; font-size: 14px; color: #848e9c;">
                                  After sending USDT to this address, click the button above to verify your payment
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>


          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container" style="display: none;">
             <li class="hidden"><span class="capa"> <a href="activation.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="activation_report.php">Report</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container" style="display: none;">
             <li class="hidden"><span class="capa"> <a href="left-network.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="right-network.php">Right Direct</a></span> </li>
             <li class="hidden"><span class="capa"><a href="total-team.php">Total Team</a></span> </li>
             <li class="hidden"><span class="capa"><a href="tree.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="non-working-bonus.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="matching-bonus.php">Matching Bonus</a></span> </li>
             <li class="hidden"><span class="capa"><a href="reward-bonus.php">Reward Bonus</a></span> </li>
             <li class="hidden"><span class="capa"><a href="lottery-bonus.php">Lottery Bonus</a></span> </li>
             <li class="hidden"><span class="capa"><a href="game-bonus.php">Game Bonus</a></span> </li>
             <li class="hidden"><span class="capa"><a href="bonus-report.php">Report</a></span> </li>

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"><a href="activation-wallet.php">Activation Wallet</a></span> </li>
             <li class="hidden"><span class="capa"><a href="cashback-wallet.php">Cashback Wallet</a></span> </li>
             <li class="hidden"><span class="capa"><a href="earning-wallet.php">Earning Wallet</a></span> </li>
             <li class="hidden"><span class="capa"><a href="gaming-wallet.php">Gaming Wallet</a></span> </li>
             <li class="hidden"><span class="capa"><a href="wallet-report.php">Report</a></span> </li>
         </div>


             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="withdrawal.php" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="withdrawal-report.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="buy-lottery.php" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="lottery-reports.php">Report</a></span> </li>
      </div>

         <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden"><span class="capa"> <a href="play-games.php" class="openMod" data-modal="buy">Play Games</a> </span> </li>
             <li class="hidden"><span class="capa"><a href="games-reports.php">Report</a></span> </li>
      </div>


            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="promo.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Promo Stuff </div>
            </div>
            </a> </li>
            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
           <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Support Ticket </div>
            </div>
            </a> </li>
            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>






        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="balance.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : Expert</div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="balance.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">
              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img src="assets/logo.png" class="image" alt=""> </div>
                </a> </div>
            </div>
            <div class="db-page-topline-panel__right__content">
              <div class="topline-lang-panel-block">
                <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="current-lang__text">EN</div>
                  </a>
                  <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                    <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                    <div class="lang-link__text">EN</div>
                    </a> </div>
                </div>
              </div>
              <div class="topline-logout-btn-block"> <a href="logout/" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
              <div class="mobile-panel-btn-block">
                <button type="button" class="mobile-panel-btn"></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
/* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content - This allows the user to have multiple dropdowns without any conflict */
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
  dropdown[
i].addEventListener("click", function() {
    this.classList.toggle("active");
	this.classList.toggle("dactive");
    var dropdownContent = this.nextElementSibling;
    if (dropdownContent.style.display === "block") {
      dropdownContent.style.display = "none";


    } else {
      dropdownContent.style.display = "block";
    }
  });
}
</script>              <div class="db-page-content-block">
                <div class="db-page-content">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>





<!-- Loading Modal -->
<div class="modal fade deposit-modal" id="monitorModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="monitoring-status">
                    <div class="custom-spinner"></div>
                    <h4 id="statusText">Checking your payment...</h4>
                    <p id="statusDetails" class="text-muted">Please wait while we verify your transaction</p>
                </div>
                <div class="monitoring-result" style="display: none;">
                    <div id="resultIcon"></div>
                    <h4 id="resultText"></h4>
                    <p id="resultDetails" class="text-muted"></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="./assets/cy/libs/jquery/jquery.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/libs/clipboard.min.js?vs=100"></script>
<script src="./assets/cy/js/common.js_vs=100"></script>

<style>
    /* Binance-inspired theme */
    body, #page-wrapper {
        background-color: #0b0e11;
        color: #eaecef;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .content-header {
        display: none;
    }

    .deposit-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /* max-width: 1200px; */
    }

    /* Deposit Header */
    .deposit-header {
        background: linear-gradient(135deg, #181c27 0%, #0b0e11 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .deposit-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .deposit-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: radial-gradient(ellipse at bottom, rgba(240, 185, 11, 0.05), transparent 70%);
        pointer-events: none;
    }

    .deposit-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .deposit-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
        font-weight: 600;
    }

    .deposit-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
        font-size: 22px;
    }

    .wallet-balance {
        background: rgba(240, 185, 11, 0.1);
        border-radius: 8px;
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        border: 1px solid rgba(240, 185, 11, 0.2);
        position: relative;
        overflow: hidden;
    }

    .wallet-balance::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(240, 185, 11, 0.05) 0%, transparent 100%);
        pointer-events: none;
    }

    .balance-value {
        font-size: 24px;
        font-weight: 600;
        color: #f0b90b;
        display: flex;
        align-items: center;
    }

    .balance-value i {
        margin-right: 8px;
        font-size: 18px;
    }

    .balance-label {
        font-size: 14px;
        color: #848e9c;
        margin-top: 4px;
    }

    /* Modal Styling */
    .deposit-modal .modal-content {
        background: #181c27;
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        color: #eaecef;
        overflow: hidden;
    }

    .deposit-modal .modal-body {
        padding: 30px;
    }

    @media (max-width: 480px) {
        .deposit-modal .modal-body {
            padding: 20px 15px;
        }

        .custom-spinner {
            width: 40px;
            height: 40px;
            margin-bottom: 15px;
        }

        .monitoring-status h4, .monitoring-result h4 {
            font-size: 18px;
            margin-top: 15px;
        }

        .monitoring-status p, .monitoring-result p {
            font-size: 12px;
        }
    }

    /* Custom spinner animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .custom-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(240, 185, 11, 0.1);
        border-radius: 50%;
        border-top: 4px solid #f0b90b;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px auto;
    }

    .monitoring-status, .monitoring-result {
        text-align: center;
    }

    .monitoring-status h4, .monitoring-result h4 {
        margin-top: 20px;
        color: #fff;
        font-size: 20px;
    }

    .text-muted {
        color: #848e9c !important;
    }

    .text-success {
        color: #0ecb81 !important;
    }

    .text-warning {
        color: #f0b90b !important;
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(240, 185, 11, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
        }
    }
</style>

<script>
function CopyToClipboard2(containerid) {
    const text = document.getElementById(containerid).innerText;
    navigator.clipboard.writeText(text).then(() => {
        // Show a stylish notification
        const copyBtn = document.getElementById(containerid + '_copy');
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied';
        copyBtn.classList.add('copied');

        // Reset after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.classList.remove('copied');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ' + err);

        // Fallback to old method if clipboard API fails
        if (window.getSelection) {
            if (window.getSelection().empty) {
                window.getSelection().empty();
            } else if (window.getSelection().removeAllRanges) {
                window.getSelection().removeAllRanges();
            }
        } else if (document.selection) {
            document.selection.empty();
        }

        if (document.selection) {
            var range = document.body.createTextRange();
            range.moveToElementText(document.getElementById(containerid));
            range.select().createTextRange();
            document.execCommand("Copy");
        } else if (window.getSelection) {
            var range = document.createRange();
            range.selectNode(document.getElementById(containerid));
            window.getSelection().addRange(range);
            document.execCommand("Copy");
            $('#'+containerid+'_copy').text('Copied');
            $('#'+containerid+'_copy').addClass('btn-success');
            $('#'+containerid+'_copy').removeClass('btn-violet');
        }
    });
}

// Payment monitoring functionality
$('#checkPayment').click(function() {
    const walletAddress = $('#_address').text();
    startMonitoring(walletAddress);
});

const MAX_RETRIES = 4;  // Total of 2 minutes (4 * 30 seconds)
let retryCount = 0;

function startMonitoring(address) {
    // Show modal
    $('#monitorModal').modal({
        backdrop: 'static',
        keyboard: false
    });

    // Reset retry count when starting fresh
    retryCount = 0;

    // Update initial status
    updateStatusMessage(retryCount);

    // Start monitoring with timeout
    $.ajax({
        url: 'deposit_block2_model.php',
        method: 'POST',
        data: {
            address: address
        },
        timeout: 30000, // 30 second timeout
        success: function(response) {
            try {
                console.log(response);

                // Check if the response contains HTML error messages
                if (response.includes('<br />') || response.includes('<b>Warning</b>')) {
                    // Extract the JSON part if it exists
                    const jsonMatch = response.match(/\{"status":.*\}/);
                    if (jsonMatch) {
                        response = jsonMatch[0];
                    } else {
                        handleRetry(address);
                        return;
                    }
                }

                const result = JSON.parse(response);
                if (result.status === 'success') {
                    showSuccess();
                } else {
                    // Check if there's no balance
                    if (result.message && result.message.includes('No significant usdt balance found')) {
                        showError('No USDT balance found in this address. Please send USDT to the address first.');
                    } else {
                        handleRetry(address);
                    }
                }
            } catch (e) {
                console.error('Error parsing response:', e);
                showError('Invalid response from server. Please try again later.');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', status, error);
            if (status === 'timeout') {
                handleRetry(address);
            } else {
                showError('Connection error occurred. Please try again later.');
            }
        }
    });
}

function handleRetry(address) {
    retryCount++;

    if (retryCount >= MAX_RETRIES) {
        showError('Payment not detected after multiple attempts. Please try again or contact support if you have already sent the payment.');
        return;
    }

    updateStatusMessage(retryCount);

    // Wait 5 seconds before trying again
    setTimeout(function() {
        startMonitoring(address);
    }, 5000);
}

function updateStatusMessage(currentRetry) {
    const timeElapsed = currentRetry * 30;
    const messages = [
        'Checking your payment...',
        'Still checking... (30 seconds elapsed)',
        'Still checking... (1 minute elapsed)',
        'Final check... (1.5 minutes elapsed)'
    ];

    $('#statusText').text(messages[currentRetry] || 'Checking payment...');
    $('#statusDetails').html(`
        <p>This may take a few moments</p>
        <small class="text-muted">Attempt ${currentRetry + 1} of ${MAX_RETRIES}</small>
    `);
}

function showSuccess() {
    $('.monitoring-status').hide();
    $('.monitoring-result').show();
    $('#resultIcon').html('<i class="fas fa-check-circle text-success fa-3x"></i>');
    $('#resultText').text('Payment Confirmed!');
    $('#resultDetails').text('Your payment has been processed successfully');

    // Reload page after 3 seconds
    setTimeout(function() {
        window.location.reload();
    }, 1000);
}

function showError(message) {
    $('.monitoring-status').hide();
    $('.monitoring-result').show();
    $('#resultIcon').html('<i class="fas fa-exclamation-circle text-warning fa-3x"></i>');
    $('#resultText').text('Payment Not Found');

    // Add a help link for common issues
    const helpText = message.includes('No USDT balance') ?
        '<div style="margin-top: 10px; font-size: 13px; color: #848e9c;">Make sure you have sent USDT to this address using the BSC network.</div>' : '';

    $('#resultDetails').html(`
        ${message}<br>
        ${helpText}
        <button class="btn btn-primary mt-3" onclick="retryFromStart()">Check Again</button>
    `);
       setTimeout(function() {
        window.location.reload();
    }, 1000);
}

function retryFromStart() {
    const walletAddress = $('#_address').text();
    $('.monitoring-result').hide();
    $('.monitoring-status').show();
    startMonitoring(walletAddress);
}
</script>



</body>
