<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$type = 0;
$title = "Rank Table";
$marr = array(0, 5000, 15000, 40000, 90000, 190000, 440000, 940000, 1940000, 4440000, 9440000);
$amtarr = array(0, 2, 5, 10, 50, 1500, 1500);
$reward = $user->reward;
$reward = 0;
$i=0;
$reward_arr = get_reward();
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-page-topline-panel__right__logo .logo-wrapper .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-left:not(:first-of-type) {
    display: none !important;
}
.db-page-right {
    width: 100% !important;
    max-width: 100% !important;
}
.db-page-right .db-page-right-content {
    padding: 0 !important;
}
.db-page-right .db-page-right-content .container {
    max-width: 100% !important;
    padding: 0 !important;
}
.db-page-right .db-page-right-content .row {
    margin: 0 !important;
}
.db-page-right .db-page-right-content .col-12 {
    padding: 0 !important;
}
.db-page-right .db-page-right-content .db-page-content {
    padding: 20px !important;
}
.db-page-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 90vh;
    padding: 20px;
}
.db-page-content-block {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.table {
    margin-bottom: 0;
}
.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
    padding: 15px;
}
.table tbody td {
    text-align: center;
    padding: 12px 15px;
    border-color: #e9ecef;
    vertical-align: middle;
}
.table tbody tr:hover {
    background-color: #f8f9fa;
}
.reward-achieved {
    font-weight: 600;
}
.reward-achieved.yes {
    color: #28a745;
}
.reward-achieved.no {
    color: #dc3545;
}
</style>
</head>
<body>

<?php include 'sidebar.php'; ?>

<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<!-- Hide old sidebar -->
<style>
.db-page-left:not(:first-of-type) {
    display: none !important;
}
</style>
<div class="db-page-right">
  <div class="db-page-right-content">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="db-page-content">
            <div class="db-page-content-block">
              <h1 class="page-title"><?php echo $title; ?></h1>

              <div class="table-container">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Rank</th>
                        <th>Target SLB 50%</th>
                        <th>Target OLB 50%</th>
                        <th>Achieved SLB</th>
                        <th>Achieved OLB</th>
                        <th>Reward Achieved</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php while($i < 10){$i++;
                          $self = $user->topup;
                          $max = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 0,1"))->amount;
                          $max = ($max) ? $max*1 : 0;
                          // $max2 = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 1,1"))->amount;
                          // $max2 = ($max2) ? $max2*1 : 0;
                          $max2 = 0;
                          $max3 = $user->teamb - $max - $max2;
                          $max3 = ($max3 > 0) ? $max3*1 : 0;
                          $mamt = $marr[$i]-$marr[$i-1];

                          if($reward < $i && ($marr[$i]*0.5) <= $max && ($marr[$i]*0.0) <= $max2 && ($marr[$i]*0.5) <= $max3){
                              $reward++;
                          }
                          $reward_status = ($reward >= $i) ? 'Yes' : 'No';
                          $reward_class = ($reward >= $i) ? 'yes' : 'no';
                      ?>
                      <tr>
                        <td><strong><?php echo $reward_arr[$i];?></strong></td>
                        <td><?php echo number_format($mamt*0.5, 2);?></td>
                        <td><?php echo number_format($mamt*0.5, 2);?></td>
                        <td><?php echo number_format(($marr[$i]*0.5 <= $max) ? $mamt*0.5 : ($max-($marr[$i-1] * 0.5) > 0 ? $max-($marr[$i-1] * 0.5) : 0), 2);?></td>
                        <td><?php echo number_format(($marr[$i]*0.5 <= $max3) ? $mamt*0.5 : ($max3-($marr[$i-1] * 0.5) > 0 ? $max3-($marr[$i-1] * 0.5) : 0), 2);?></td>
                        <td><span class="reward-achieved <?php echo $reward_class; ?>"><?php echo $reward_status;?></span></td>
                      </tr>
                      <?php }?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

</div>
</div>
</div>
</div>
</div>

<!-- jQuery -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- Bootstrap JS -->
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>

<!-- Other required scripts -->
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.polyfilled.min.js?vs=100"></script>
<script src="./assets/cy/libs/chart.js-3.7.1/dist/chart.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.17/jquery.inputmask.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        $(document).ready(function() {
            console.log('Document ready - Rank Table page initialized');
        });
    }

    // Initialize the app
    initializeApp();
})();
</script>

<script>
    function show(){
       document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }
    function closeSidebar(){
        document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
</script>

</body>
</html>