<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$type = 0;
$title = "Rank Table";
$marr = array(0, 5000, 15000, 40000, 90000, 190000, 440000, 940000, 1940000, 4440000, 9440000);
$amtarr = array(0, 2, 5, 10, 50, 1500, 1500);
$reward = $user->reward;
$reward = 0;
$i=0;
$reward_arr = get_reward();

// Calculate total ranks achieved
$total_ranks_achieved = 0;
$temp_reward = 0;
$temp_i = 0;
while($temp_i < 10){
    $temp_i++;
    $self = $user->topup;
    $max = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 0,1"))->amount;
    $max = ($max) ? $max*1 : 0;
    $max2 = 0;
    $max3 = $user->teamb - $max - $max2;
    $max3 = ($max3 > 0) ? $max3*1 : 0;

    if($temp_reward < $temp_i && ($marr[$temp_i]*0.5) <= $max && ($marr[$temp_i]*0.0) <= $max2 && ($marr[$temp_i]*0.5) <= $max3){
        $temp_reward++;
        $total_ranks_achieved++;
    }
}

// Calculate total target amount
$total_target = 0;
for($j = 1; $j <= 10; $j++) {
    $mamt = $marr[$j] - $marr[$j-1];
    $total_target += $mamt * 0.5; // SLB 50%
}

include 'sidebar1.php'
?>



<style>
  * Team Header Styling */
.team-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.team-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.team-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.team-title h2 {
    font-size: 24px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.team-title h2 i {
    margin-right: 12px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.stat-label {
    font-size: 14px;
    color: #848e9c;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.stat-label i {
    margin-right: 8px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #eaecef;
    display: flex;
    align-items: baseline;
}

.stat-value.earnings {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

/* Team Card */
.team-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .team-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .team-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .team-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .team-table tr:last-child td {
        border-bottom: none;
    }

    .team-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Rank Badge */
    .rank-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(23, 201, 100, 0.1);
        color: #17c964;
        border: 1px solid rgba(23, 201, 100, 0.2);
    }

    /* Amount Value */
    .amount-value {
        font-weight: 600;
        background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-badge.achieved {
        background-color: rgba(23, 201, 100, 0.1);
        color: #17c964;
        border: 1px solid rgba(23, 201, 100, 0.2);
    }

    .status-badge.not-achieved {
        background-color: rgba(255, 69, 58, 0.1);
        color: #ff453a;
        border: 1px solid rgba(255, 69, 58, 0.2);
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .team-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .team-header {
            padding: 20px;
        }

        .team-table {
            display: block;
            overflow-x: auto;
        }

        .team-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }
</style>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">View your rank progression and achievement status across different levels.</div>

                          <!-- Team Header with Stats -->
                          <div class="team-header">
                              <div class="team-title">
                                  <h2><i class="fas fa-medal"></i> <?php echo $title; ?></h2>
                              </div>
                              <div class="team-stats">
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-trophy"></i> Ranks Achieved</div>
                                      <div class="stat-value earnings"><?php echo $total_ranks_achieved; ?><span class="unit">/ 10</span></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-target"></i> Total Target</div>
                                      <div class="stat-value"><span class="currency">$</span><?php echo number_format($total_target, 2); ?></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-chart-line"></i> Current Team</div>
                                      <div class="stat-value"><span class="currency">$</span><?php echo number_format($user->teamb, 2); ?></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-percentage"></i> Progress</div>
                                      <div class="stat-value earnings"><?php echo round(($total_ranks_achieved / 10) * 100, 1); ?>%</div>
                                  </div>
                              </div>
                          </div>

                          <!-- Rank Table Card -->
                          <div class="team-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-table"></i> Rank Achievement Table</h3>
                              </div>
                              <div class="card-body">
                                  <table class="team-table">
                                      <thead>
                                          <tr>
                                              <th>Rank</th>
                                              <th>Target SLB 50%</th>
                                              <th>Target OLB 50%</th>
                                              <th>Achieved SLB</th>
                                              <th>Achieved OLB</th>
                                              <th>Status</th>
                                          </tr>
                                      </thead>
                                      <tbody>
                                          <?php while($i < 10){$i++;
                                              $self = $user->topup;
                                              $max = @mysqli_fetch_object(mysqli_query($link, "SELECT (teamb+topup) AS amount FROM user WHERE refer_id = '".$uid."' AND status = 0 ORDER BY (teamb+topup) DESC LIMIT 0,1"))->amount;
                                              $max = ($max) ? $max*1 : 0;
                                              $max2 = 0;
                                              $max3 = $user->teamb - $max - $max2;
                                              $max3 = ($max3 > 0) ? $max3*1 : 0;
                                              $mamt = $marr[$i]-$marr[$i-1];

                                              if($reward < $i && ($marr[$i]*0.5) <= $max && ($marr[$i]*0.0) <= $max2 && ($marr[$i]*0.5) <= $max3){
                                                  $reward++;
                                              }
                                              $reward_status = ($reward >= $i) ? 'Achieved' : 'Not Achieved';
                                              $reward_class = ($reward >= $i) ? 'achieved' : 'not-achieved';
                                          ?>
                                          <tr>
                                              <td><span class="rank-badge"><?php echo $reward_arr[$i];?></span></td>
                                              <td><span class="amount-value">$ <?php echo number_format($mamt*0.5, 2);?></span></td>
                                              <td><span class="amount-value">$ <?php echo number_format($mamt*0.5, 2);?></span></td>
                                              <td><span class="amount-value">$ <?php echo number_format(($marr[$i]*0.5 <= $max) ? $mamt*0.5 : ($max-($marr[$i-1] * 0.5) > 0 ? $max-($marr[$i-1] * 0.5) : 0), 2);?></span></td>
                                              <td><span class="amount-value">$ <?php echo number_format(($marr[$i]*0.5 <= $max3) ? $mamt*0.5 : ($max3-($marr[$i-1] * 0.5) > 0 ? $max3-($marr[$i-1] * 0.5) : 0), 2);?></span></td>
                                              <td><span class="status-badge <?php echo $reward_class; ?>"><?php echo $reward_status;?></span></td>
                                          </tr>
                                          <?php }?>
                                      </tbody>
                                  </table>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>




<script src="./assets/cy/libs/jquery/jquery-3.6.0.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/autosize/dist/autosize.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="./assets/cy/libs/chart.js-3.7.1/dist/chart.min.js?vs=100"></script>
<script src="./assets/cy/libs/clipboard.js-master/dist/clipboard.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/js/common.js?vs=100"></script>

</body>
</html>