<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$type = (isset($_GET['type']) && (int) $_GET['type'] <= 3) ? (int) $_GET['type'] : 0;
$title = ($type == 3) ? "Pool Income" : (($type == 2) ? "Special Reward Income" : (($type == 1) ? "Salary Income" : "Reward Income"));
$query = "SELECT r.* FROM income_royalty as r"
        . " WHERE r.uid='".$uid."' AND r.type=".$type
        . " ORDER BY r.datetime DESC";
$result = my_query($query);
$i=0;

$reward_arr = get_reward();
$reward_arr2 = array('-', 'Earbuds', 'Smart Phone', 'Laptop', 'Bike', 'Royal Enfield', 'Taigo', 'Thar', 'Fortuner', 'BMW x1', 'Three BHK and Mercedes Benz');

// Calculate total earnings
$total_earnings_query = "SELECT SUM(amount) as total FROM income_royalty WHERE uid='$uid' AND type=$type";
$total_earnings_result = my_query($total_earnings_query);
$total_earnings_row = mysqli_fetch_object($total_earnings_result);
$total_earnings = $total_earnings_row->total ? $total_earnings_row->total : 0;

// Get total number of transactions
$total_transactions = mysqli_num_rows($result);

// Get unique ranks count (for reward income)
$unique_ranks = 0;
if($type == 0) {
    $unique_ranks_query = "SELECT COUNT(DISTINCT level) as count FROM income_royalty WHERE uid='$uid' AND type=$type";
    $unique_ranks_result = my_query($unique_ranks_query);
    $unique_ranks_row = mysqli_fetch_object($unique_ranks_result);
    $unique_ranks = $unique_ranks_row->count ? $unique_ranks_row->count : 0;
}

// Get latest transaction date
$latest_transaction_query = "SELECT MAX(datetime) as latest FROM income_royalty WHERE uid='$uid' AND type=$type";
$latest_transaction_result = my_query($latest_transaction_query);
$latest_transaction_row = mysqli_fetch_object($latest_transaction_result);
$latest_transaction = $latest_transaction_row->latest ? date("d M, Y", strtotime($latest_transaction_row->latest)) : 'N/A';
include 'sidebar1.php'
?>

  

<style>
  * Team Header Styling */
.team-header {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    padding: 25px 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.team-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.team-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.team-title h2 {
    font-size: 24px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.team-title h2 i {
    margin-right: 12px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(23, 201, 100, 0.3);
}

.stat-label {
    font-size: 14px;
    color: #848e9c;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.stat-label i {
    margin-right: 8px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #eaecef;
    display: flex;
    align-items: baseline;
}

.stat-value.earnings {
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

/* Team Card */
.team-card {
    background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #17c964, #0170ef, transparent);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-header h3 i {
    margin-right: 10px;
    background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .team-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .team-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .team-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .team-table tr:last-child td {
        border-bottom: none;
    }

    .team-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Rank Badge */
    .rank-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(23, 201, 100, 0.1);
        color: #17c964;
        border: 1px solid rgba(23, 201, 100, 0.2);
    }

    /* Amount Value */
    .amount-value {
        font-weight: 600;
        background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Empty State */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #848e9c;
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: rgba(240, 185, 11, 0.3);
    }

    .empty-text {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .team-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .team-header {
            padding: 20px;
        }

        .team-table {
            display: block;
            overflow-x: auto;
        }

        .team-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }
</style>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding : 5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">View your earnings and transaction history from reward bonuses.</div>

                          <!-- Team Header with Stats -->
                          <div class="team-header">
                              <div class="team-title">
                                  <h2><i class="fas fa-trophy"></i> <?php echo $title; ?></h2>
                              </div>
                              <div class="team-stats">
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-coins"></i> Total Earnings</div>
                                      <div class="stat-value earnings"><span class="currency"><?php echo ($type == 2) ? SITE_CURRENCY_TKN : SITE_CURRENCY; ?></span><?php echo number_format($total_earnings, 2); ?></div>
                                  </div>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-exchange-alt"></i> Transactions</div>
                                      <div class="stat-value"><?php echo $total_transactions; ?></div>
                                  </div>
                                  <?php if($type == 0): ?>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-medal"></i> Unique Ranks</div>
                                      <div class="stat-value"><?php echo $unique_ranks; ?></div>
                                  </div>
                                  <?php else: ?>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-chart-line"></i> Income Type</div>
                                      <div class="stat-value" style="font-size: 16px;"><?php echo ($type == 3) ? "Pool" : (($type == 2) ? "Special" : "Lifetime"); ?></div>
                                  </div>
                                  <?php endif; ?>
                                  <div class="stat-card">
                                      <div class="stat-label"><i class="fas fa-clock"></i> Latest Transaction</div>
                                      <div class="stat-value" style="font-size: 18px;"><?php echo $latest_transaction; ?></div>
                                  </div>
                              </div>
                          </div>

                          <!-- Transaction History Card -->
                          <div class="team-card">
                              <div class="card-header">
                                  <h3><i class="fas fa-history"></i> Transaction History</h3>
                                  <div class="card-header-actions">
                                      <!--<select class="card-filter" onchange="window.location.href='report_royalty.php?type='+this.value">-->
                                      <!--    <option value="0" <?php if($type == 0) echo 'selected'; ?>>Reward Income</option>-->
                                      <!--    <option value="1" <?php if($type == 1) echo 'selected'; ?>>Lifetime Cash Back Income</option>-->
                                      <!--    <option value="2" <?php if($type == 2) echo 'selected'; ?>>Special Reward Income</option>-->
                                      <!--    <option value="3" <?php if($type == 3) echo 'selected'; ?>>Pool Income</option>-->
                                      <!--</select>-->
                                  </div>
                              </div>
                              <div class="card-body">
                                  <table class="team-table">
                                      <thead>
                                          <tr>
                                              <th width="50">#</th>
                                              <th>Date</th>
                                              <th><?php echo ($type == 2) ? SITE_CURRENCY_TKN : SITE_CURRENCY;?></th>
                                              <?php if($type == 0){?><th>Rank</th><?php }?>
                                              <?php if($type == 0 && 0){?><th>Month</th><?php }?>
                                          </tr>
                                      </thead>
                                      <tbody>
                                          <?php while ($row = mysqli_fetch_object($result)){$i++;?>
                                          <tr>
                                              <td><?php echo $i;?></td>
                                              <td><?php echo date("d M, Y h:i A", strtotime($row->datetime));?></td>
                                              <td><span class="amount-value"> $ <?php echo number_format($row->amount*1, 2);?></span></td>
                                              <?php if($type == 0){?><td><span class="rank-badge"><?php echo $reward_arr[$row->level];?></span></td><?php }?>
                                              <?php if($type == 0 && 0){?><td><?php echo $row->days;?></td><?php }?>
                                          </tr>
                                          <?php }?>
                                      </tbody>
                                  </table>

                                  <!-- Empty state (will only show if there are no transactions) -->
                                  <?php if ($total_transactions == 0): ?>
                                  <div class="empty-state">
                                      <div class="empty-icon"><i class="fas fa-trophy"></i></div>
                                      <div class="empty-text">No transactions found</div>
                                      <div class="empty-subtext">You don't have any <?php echo strtolower($title); ?> transactions yet</div>
                                  </div>
                                  <?php endif; ?>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>




<script src="./assets/cy/libs/jquery/jquery-3.6.0.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/autosize/dist/autosize.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="./assets/cy/libs/chart.js-3.7.1/dist/chart.min.js?vs=100"></script>
<script src="./assets/cy/libs/clipboard.js-master/dist/clipboard.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/js/common.js?vs=100"></script>

</body>
</html>