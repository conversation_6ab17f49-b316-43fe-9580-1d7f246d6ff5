<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$type = (isset($_GET['type']) && in_array($_GET['type'], array(1,2,3,4,5,6,7,8,12))) ? $_GET['type'] : 1;
$typearr = array(1 => 'USDT', 2 => 'LTC', 3 => 'DOGE', 4 => 'ETH', 5 => 'BCH', 6 => 'Dash', 7 => 'XRP', 8 => 'NEO', 12 => 'TRX');
$alt_color = array(1 => '#605CA8', 2 => '#0073B7', 3 => '#F39C12', 4 => '#605CA8', 5 => '#0073B7', 6 => '#F39C12', 7 => '#262D4E', 8 => '#FF851B', 12 => '#FF851B');
$type2 = $typearr[$type];
$title = "Add Fund by ".$type2.' (Network BEP20)';

include_once '../lib/own_pay/own_pay.php';
$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

if(empty($user->pay_address)){
     $wallet = generateNewWallet();
      $sql = "UPDATE user SET `pay_address` = '".$wallet['address']."', `pay_privatekey` = '".$wallet['privateKey']."' WHERE uid = '".$uid."'" ;
      my_query($sql);
      redirect('./deposit_block.php');
}
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Deposit Funds</title><meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.1/css/all.min.css" integrity="sha512-9my9Mb2+0YO+I4PUCSwUYO7sEK21Y0STBAiFEYoWtd2VzLEZZ4QARDrZ30hdM1GlioHJ8o8cWQiy8IAb1hy/Hg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  /*background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);**/
  border-radius:5px;}
/* Optional: Style the caret down icon */


/* Some media queries for responsiveness */
@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;

}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

</style>
<link rel="shortcut icon" href="./assets/fav.png" />
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<?php include 'sidebar.php'; ?>


<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<!-- Hide old sidebar -->
<style>
.db-page-left:not(:first-of-type) {
    display: none !important;
}
</style>
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a	href='dashboard.php'													class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>

            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a	href="javascript:void(0)"													class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_level.php">Level Income</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>
         </div>


             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>


            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>






        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo isset($user->reward) ? ($reward_arr[$user->reward] ?? 'Starter') : 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
            <div class="topline-user-panel-block">
              <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>
                </a>
                <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>
                  <div class="user-link__text">Profile</div>
                  </a> <a href="logout.php" class="user-link">
                  <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>
                  <div class="user-link__text">Logout</div>
                  </a> </div>
              </div>
            </div>
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-left">
                        <div class="refill-info-block">
                          <div class="refill-info">
                            <h1> Deposit </h1>
                            <div class="refill-info-descr"> Deposit your account, invest, play, earn, and enjoy the experience. </div>
                            <div class="account-info-balance-block">
                              <div class="account-info-balance-title"> Available balance </div>
                              <div class="account-info-balance-items-block">
                                <div class="account-info-balance-items">
                                  <div class="account-info-balance-item-wrapper">
                                    <div class="account-info-balance-item">
                                      <div class="account-info-balance-item-left">
                                        <div class="account-info-balance-item-currency">
                                          <div class="account-info-balance-item-currency__icon"> <img class="image" src="assets/cy/images/svg/payment/usdt.svg" alt=""> </div>
                                          <div class="account-info-balance-item-currency__amount userInfoBalance" data-currency="usdt"> <?php echo isset($user->wallet_topup) ? round($user->wallet_topup*1, 2) : '0.00'; ?> </div>
                                        </div>
                                      </div>
                                      <div class="account-info-balance-item-right">
                                        <div class="account-info-balance-item-abbr"> USDT </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="refill-right">
                        <div class="refill-content-block">
                          <div class="refill-form-block">
                            <div class="h2 refill-form-title"> Deposit your account </div>
                            <div class="refill-form form blockTopupBalance">
                              <div class="field-block">
                                <!-- <div class="field-title-block">
                                  <div class="field-title"> Asset <span class="field-required-star">*</span> </div>
                                </div> -->
                                <div class="field field--select">
                                  <div class="dropdown bootstrap-select select-currency">
                                    <select name="currency" class="select-currency">
                                      <option data-content="<div class=&quot;select-currency-list-item&quot;><div class=&quot;select-currency-list-item__left&quot;><div class=&quot;select-currency-list-item__icon select-currency-list-item__icon--usdt&quot;><img class=&quot;image&quot; src=&quot;assets/cy/images/svg/payment/usdt.svg&quot; alt=&quot;&quot;></div><div class=&quot;select-currency-list-item__arrow&quot;></div></div><div class=&quot;select-currency-list-item__right&quot;><div class=&quot;select-currency-list-item__title&quot;>USDT</div></div></div>" value="usdt">USDT</option>
                                    </select>
                                    <div class="dropdown-menu ">
                                      <div class="inner show" role="listbox" id="bs-select-4" tabindex="-1">
                                        <ul class="dropdown-menu inner show" role="presentation">
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="field-block">
                                <!-- <div class="field-title-block">
                                  <div class="field-title"> Blockchain <span class="field-required-star">*</span> </div>
                                </div> -->
                                <div class="field field--select field--protocol">
                                  <div class="field-icon"></div>
                                  <div class="dropdown bootstrap-select select">
                                    <select name="payment" class="select">
                                      <option data-currency="usdt" data-content="<div class=&quot;select-item&quot;><div class=&quot;select-item-text&quot;>BSC (BEP20)</div></div>" value="tether_bep-20_usdt">BSC </option>

                                    </select>
                                    <div class="dropdown-menu ">
                                      <div class="inner show" role="listbox" id="bs-select-5" tabindex="-1">
                                        <ul class="dropdown-menu inner show" role="presentation">
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="field-message-block sendOnlyNetworkInfo" data-message="Send [currency] on the [network] blockchain, otherwise your funds will be lost."> <br>
                                <div class="field-message">
                                  <div class="field-message__icon"></div>
                                  <div class="field-message__text">Send <strong>USDT</strong> on the <strong>BSC</strong> blockchain, otherwise your funds will be lost.</div>
                                </div>
                              </div>
                              <div class="field-block">
                                <div class="field-title-block">
                                  <div class="field-title"> Address </div>
                                </div>
                                <div class="field field--textarea field--have-icon field--wallet">
                                  <div class="field-icon"></div>
                                  <textarea name="wallet" placeholder="" data-waiting="Waiting for address..." style="overflow: hidden; overflow-wrap: break-word; height: 56px;"><?php echo isset($user->pay_address) ? $user->pay_address : 'Generating address...'; ?></textarea>
                                  <div class="field-right-panel-block">
                                    <div class="field-right-panel">
                                      <div class="copy-field-btn-block">
                                        <button class="copy-field-btn purple-btn" data-clipboard-text="<?php echo isset($user->pay_address) ? $user->pay_address : ''; ?>" aria-label="Successfully" onclick="CopyToClipboard2('wallet_address');"></button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="field-message-block sendConfirmations" data-message="Your deposit will be credited after [conf] network confirmation(s). This usually takes 1-15 minutes."> <br>
                                <div class="field-message">
                                  <div class="field-message__icon"></div>
                                  <div class="field-message__text">Your deposit will be credited after <strong>10</strong> network confirmation(s). This usually takes 1-15 minutes.</div>
                                </div>
                              </div>

                              <!-- QR Code Section -->
                              <div class="field-block" style="text-align: center; margin-top: 30px;">
                                <div class="field-title-block">
                                  <div class="field-title"> QR Code </div>
                                </div>
                                <div style="display: flex; justify-content: center; margin: 20px 0;">
                                  <img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=<?php echo isset($user->pay_address) ? $user->pay_address : ''; ?>" alt="QR Code" style="border-radius: 8px; padding: 15px; background: #fff; max-width: 180px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);" />
                                </div>
                                <p style="color: #848e9c; font-size: 14px; margin-top: 15px;">
                                  Scan this QR code with your wallet app to send USDT
                                </p>
                              </div>

                              <!-- Check Payment Button -->
                              <div class="field-block" style="text-align: center; margin-top: 30px;">
                                <button id="checkPayment" type="button" class="btn btn-primary btn-lg" style="width: 100%; padding: 15px; background: linear-gradient(135deg, #f0b90b 0%, #fbda3c 100%); border: none; color: #000; font-weight: 600; border-radius: 8px;">
                                  <i class="fas fa-sync-alt"></i> Check Payment Status
                                </button>
                                <p style="color: #848e9c; font-size: 14px; margin-top: 15px;">
                                  After sending USDT to this address, click the button above to verify your payment
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>


</div>

<!-- Loading Modal -->
<div class="modal fade deposit-modal" id="monitorModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Verification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="monitoring-status">
                    <div class="custom-spinner"></div>
                    <h4 id="statusText">Checking your payment...</h4>
                    <p id="statusDetails" class="text-muted">Please wait while we verify your transaction</p>
                </div>
                <div class="monitoring-result" style="display: none;">
                    <div id="resultIcon"></div>
                    <h4 id="resultText"></h4>
                    <p id="resultDetails" class="text-muted"></p>
                </div>
            </div>
            <div class="modal-footer" id="modalFooter" style="display: none;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="retryFromStart()">Try Again</button>
            </div>
        </div>
    </div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            // Initialize clipboard for copy buttons
            var clipboard = new ClipboardJS('.copy-field-btn');

            clipboard.on('success', function(e) {
                toastr.success('Address copied to clipboard!');
                e.clearSelection();
            });

            clipboard.on('error', function(e) {
                toastr.error('Failed to copy address');
            });

            // Add keyboard support for modal
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#monitorModal').hasClass('show')) {
                    closeModal();
                }
            });

            // Test if button exists
            console.log('Button exists:', $('#checkPayment').length);
            console.log('Modal exists:', $('#monitorModal').length);
            console.log('Dropdown buttons found:', $('.dropdown-btn').length);
        });

        function CopyToClipboard2(containerid) {
            const text = document.querySelector('textarea[name="wallet"]').value;
            navigator.clipboard.writeText(text).then(() => {
                toastr.success('Address copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ' + err);

                // Fallback to old method if clipboard API fails
                if (window.getSelection) {
                    if (window.getSelection().empty) {
                        window.getSelection().empty();
                    } else if (window.getSelection().removeAllRanges) {
                        window.getSelection().removeAllRanges();
                    }
                } else if (document.selection) {
                    document.selection.empty();
                }

                if (document.selection) {
                    var range = document.body.createTextRange();
                    range.moveToElementText(document.querySelector('textarea[name="wallet"]'));
                    range.select().createTextRange();
                    document.execCommand("Copy");
                } else if (window.getSelection) {
                    var range = document.createRange();
                    range.selectNode(document.querySelector('textarea[name="wallet"]'));
                    window.getSelection().addRange(range);
                    document.execCommand("Copy");
                    toastr.success('Address copied to clipboard!');
                }
            });
        }

        // Make function global
        window.CopyToClipboard2 = CopyToClipboard2;

        // Payment monitoring functionality - Single event handler
        $('#checkPayment').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Check Payment button clicked!');
            const walletAddress = $('textarea[name="wallet"]').val();
            console.log('Wallet address:', walletAddress);

            if (!walletAddress || walletAddress.trim() === '' || walletAddress === 'Generating address...') {
                toastr.error('Wallet address not ready. Please refresh the page.');
                return;
            }

            startMonitoring(walletAddress);
        });

        const MAX_RETRIES = 4;
        let retryCount = 0;

        function startMonitoring(address) {
            console.log('startMonitoring called with address:', address);

            // Show modal with multiple approaches
            try {
                // First try Bootstrap 5 modal
                const modalElement = document.getElementById('monitorModal');
                if (modalElement) {
                    // Try Bootstrap 5 first
                    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                        const modal = new bootstrap.Modal(modalElement, {
                            backdrop: 'static',
                            keyboard: false
                        });
                        modal.show();
                        console.log('Modal shown using Bootstrap 5');
                    }
                    // Fallback to jQuery modal
                    else if (typeof $ !== 'undefined' && $.fn.modal) {
                        $('#monitorModal').modal({
                            backdrop: 'static',
                            keyboard: false
                        });
                        console.log('Modal shown using jQuery/Bootstrap 4');
                    }
                    // Manual modal show as last resort
                    else {
                        modalElement.style.display = 'block';
                        modalElement.classList.add('show');
                        modalElement.setAttribute('aria-hidden', 'false');
                        document.body.classList.add('modal-open');

                        // Add backdrop
                        const backdrop = document.createElement('div');
                        backdrop.className = 'modal-backdrop fade show';
                        backdrop.id = 'modalBackdrop';
                        document.body.appendChild(backdrop);

                        console.log('Modal shown manually');
                    }
                } else {
                    console.error('Modal element not found');
                    toastr.info('Checking payment... Please wait.');
                }
            } catch (e) {
                console.error('Error showing modal:', e);
                toastr.info('Checking payment... Please wait.');
            }

            // Reset retry count when starting fresh
            retryCount = 0;

            // Update initial status
            updateStatusMessage(retryCount);

            // Start monitoring with timeout
            console.log('Making AJAX call to deposit_block2_model.php with address:', address);
            $.ajax({
                url: 'deposit_block2_model.php',
                method: 'POST',
                data: {
                    address: address
                },
                timeout: 30000,
                beforeSend: function() {
                    console.log('AJAX request started...');
                },
                success: function(response) {
                    try {
                        console.log('AJAX Success - Raw response:', response);

                        // Check if the response contains HTML error messages
                        if (response.includes('<br />') || response.includes('<b>Warning</b>')) {
                            const jsonMatch = response.match(/\{"status":.*\}/);
                            if (jsonMatch) {
                                response = jsonMatch[0];
                                console.log('Extracted JSON from response:', response);
                            } else {
                                console.log('No JSON found in response, retrying...');
                                handleRetry(address);
                                return;
                            }
                        }

                        const result = JSON.parse(response);
                        console.log('Parsed result:', result);

                        if (result.status === 'success') {
                            console.log('Payment successful!');
                            showSuccess();
                        } else {
                            console.log('Payment not found, result:', result);
                            if (result.message && result.message.includes('No significant usdt balance found')) {
                                showError('No USDT balance found in this address. Please send USDT to the address first.', result.message);
                            } else {
                                showError('Payment not detected. Please try again.', result.message || 'Unknown error');
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        console.error('Raw response was:', response);
                        showError('Invalid response from server. Please try again later.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);
                    if (status === 'timeout') {
                        console.log('Request timed out, retrying...');
                        handleRetry(address);
                    } else {
                        showError('Connection error occurred. Please try again later.');
                    }
                }
            });
        }

        function handleRetry(address) {
            retryCount++;

            if (retryCount >= MAX_RETRIES) {
                showError('Payment not detected after multiple attempts. Please try again or contact support if you have already sent the payment.');
                return;
            }

            updateStatusMessage(retryCount);

            setTimeout(function() {
                startMonitoring(address);
            }, 5000);
        }

        function updateStatusMessage(currentRetry) {
            const messages = [
                'Checking your payment...',
                'Still checking... (30 seconds elapsed)',
                'Still checking... (1 minute elapsed)',
                'Final check... (1.5 minutes elapsed)'
            ];

            $('#statusText').text(messages[currentRetry] || 'Checking payment...');
            $('#statusDetails').html(`
                <p>This may take a few moments</p>
                <small class="text-muted">Attempt ${currentRetry + 1} of ${MAX_RETRIES}</small>
            `);
        }

        function showSuccess() {
            $('.monitoring-status').hide();
            $('.monitoring-result').show();
            $('#modalFooter').show();
            $('#resultIcon').html('<i class="fas fa-check-circle text-success fa-3x"></i>');
            $('#resultText').text('Payment Confirmed!');
            $('#resultDetails').text('Your payment has been processed successfully');

            // Update footer buttons for success
            $('#modalFooter').html(`
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
            `);
        }

        function showError(message, serverResponse) {
            $('.monitoring-status').hide();
            $('.monitoring-result').show();
            $('#modalFooter').show();
            $('#resultIcon').html('<i class="fas fa-exclamation-circle text-warning fa-3x"></i>');
            $('#resultText').text('Payment Not Found');

            const helpText = message.includes('No USDT balance') ?
                '<div style="margin-top: 10px; font-size: 13px; color: #848e9c;">Make sure you have sent USDT to this address using the BSC network.</div>' : '';

            const serverInfo = serverResponse ?
                `<div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px; font-size: 12px; color: #ccc;">
                    <strong>Server Response:</strong><br>${serverResponse}
                </div>` : '';

            $('#resultDetails').html(`
                ${message}<br>
                ${helpText}
                ${serverInfo}
            `);

            // Update footer buttons for error
            $('#modalFooter').html(`
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="retryFromStart()">Check Again</button>
            `);

            console.log('Error shown, waiting for user action');
        }

        function retryFromStart() {
            const walletAddress = $('textarea[name="wallet"]').val();
            $('.monitoring-result').hide();
            $('.monitoring-status').show();
            $('#modalFooter').hide();
            startMonitoring(walletAddress);
        }

        function closeModal() {
            console.log('Closing modal...');

            try {
                // Try Bootstrap 5 modal close
                const modalElement = document.getElementById('monitorModal');
                if (modalElement && typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                        console.log('Modal closed using Bootstrap 5');
                        return;
                    }
                }

                // Try jQuery modal close
                if (typeof $ !== 'undefined' && $.fn.modal) {
                    $('#monitorModal').modal('hide');
                    console.log('Modal closed using jQuery');
                    return;
                }

                // Manual modal close
                if (modalElement) {
                    modalElement.style.display = 'none';
                    modalElement.classList.remove('show');
                    modalElement.setAttribute('aria-hidden', 'true');
                    document.body.classList.remove('modal-open');

                    // Remove backdrop
                    const backdrop = document.getElementById('modalBackdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }

                    // Remove any existing backdrops
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => backdrop.remove());

                    console.log('Modal closed manually');
                }
            } catch (e) {
                console.error('Error closing modal:', e);
            }

            // Reset modal state
            $('.monitoring-result').hide();
            $('.monitoring-status').show();
            $('#modalFooter').hide();
            $('#statusText').text('Checking your payment...');
            $('#statusDetails').html('<p class="text-muted">Please wait while we verify your transaction</p>');
        }

        // Make functions global
        window.startMonitoring = startMonitoring;
        window.retryFromStart = retryFromStart;
        window.closeModal = closeModal;
    }

    // Initialize the app
    initializeApp();
})();
</script>

<style>
/* Modal Styling - Enhanced for visibility */
.deposit-modal {
    z-index: 9999 !important;
}

.deposit-modal .modal-dialog {
    z-index: 10000 !important;
    margin: 1.75rem auto;
}

.deposit-modal .modal-content {
    background: #181c27 !important;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    color: #eaecef;
    overflow: hidden;
    position: relative;
    z-index: 10001 !important;
}

.deposit-modal .modal-header {
    background: #1e2329;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
}

.deposit-modal .modal-title {
    color: #f0b90b;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.deposit-modal .btn-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.deposit-modal .btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f0b90b;
}

.deposit-modal .modal-body {
    padding: 30px;
}

.deposit-modal .modal-footer {
    background: #1e2329;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 30px;
    border-radius: 0 0 12px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.deposit-modal .modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.deposit-modal .modal-footer .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #eaecef;
}

.deposit-modal .modal-footer .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

.deposit-modal .modal-footer .btn-primary {
    background: linear-gradient(135deg, #f0b90b 0%, #fbda3c 100%);
    color: #000;
}

.deposit-modal .modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #e6b00a 0%, #f0c52e 100%);
    transform: translateY(-1px);
}

/* Ensure modal backdrop is visible */
.modal-backdrop {
    z-index: 9998 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Manual modal styles for fallback */
.modal.show {
    display: block !important;
}

.modal-open {
    overflow: hidden;
}

@media (max-width: 480px) {
    .deposit-modal .modal-body {
        padding: 20px 15px;
    }

    .custom-spinner {
        width: 40px;
        height: 40px;
        margin-bottom: 15px;
    }

    .monitoring-status h4, .monitoring-result h4 {
        font-size: 18px;
        margin-top: 15px;
    }

    .monitoring-status p, .monitoring-result p {
        font-size: 12px;
    }
}

/* Custom spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.custom-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(240, 185, 11, 0.1);
    border-radius: 50%;
    border-top: 4px solid #f0b90b;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

.monitoring-status, .monitoring-result {
    text-align: center;
}

.monitoring-status h4, .monitoring-result h4 {
    margin-top: 20px;
    color: #fff;
    font-size: 20px;
}

.text-muted {
    color: #848e9c !important;
}

.text-success {
    color: #0ecb81 !important;
}

.text-warning {
    color: #f0b90b !important;
}
</style>


</body>
</html>